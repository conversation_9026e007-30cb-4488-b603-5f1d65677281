# SketchUp 材质浏览器 - 安装指南

## 系统要求

- SketchUp 2017 或更高版本
- Windows 10/11 或 macOS 10.14+
- Node.js 16+ (仅开发时需要)

## 安装步骤

### 1. 下载插件文件

将整个 `plugin` 文件夹复制到 SketchUp 插件目录：

**Windows:**
```
C:\Users\<USER>\AppData\Roaming\SketchUp\SketchUp [版本]\SketchUp\Plugins\
```

**macOS:**
```
~/Library/Application Support/SketchUp [版本]/SketchUp/Plugins/
```

### 2. 构建前端资源

在项目根目录的 `frontend` 文件夹中运行：

```bash
cd frontend
npm install
npm run build
```

这将在 `frontend/dist` 目录中生成构建后的文件。

### 3. 重启 SketchUp

重启 SketchUp 以加载插件。

### 4. 使用插件

插件加载后，你可以通过以下方式打开材质浏览器：

- **菜单栏**: 插件 → 材质浏览器
- **工具栏**: 点击材质浏览器图标（如果工具栏可见）

## 功能说明

### 主要功能

1. **材质浏览**: 以网格形式展示所有材质
2. **搜索过滤**: 通过名称搜索材质
3. **分类筛选**: 按纹理、纯色、透明等分类
4. **材质应用**: 一键应用材质到选中对象
5. **材质管理**: 创建、删除、导入、导出材质

### 界面说明

- **顶部工具栏**: 搜索框、导入按钮、新建按钮
- **分类导航**: 全部、纹理、纯色、透明分类
- **材质网格**: 材质卡片展示区域
- **材质卡片**: 显示材质预览、名称和操作按钮

### 操作说明

1. **应用材质**:
   - 选择模型中的对象
   - 点击材质卡片或应用按钮

2. **创建材质**:
   - 点击"新建"按钮
   - 设置材质名称、颜色、透明度
   - 点击"创建"

3. **导入材质**:
   - 点击"导入"按钮
   - 选择 .skm 材质文件或图片文件

4. **导出材质**:
   - 点击材质卡片的更多操作按钮
   - 选择"导出"
   - 选择保存位置

## 开发说明

### 项目结构

```
材质浏览器/
├── plugin/                    # SketchUp Ruby 插件
│   ├── material_browser.rb    # 插件入口文件
│   └── lib/
│       ├── material_browser_main.rb  # 主要功能实现
│       └── icons/             # 图标文件
├── frontend/                  # Vue.js 前端
│   ├── src/
│   │   ├── components/        # Vue 组件
│   │   ├── assets/           # 样式和资源
│   │   └── App.vue           # 主应用组件
│   ├── dist/                 # 构建输出目录
│   └── package.json          # 前端依赖配置
└── README.md
```

### 技术栈

- **后端**: Ruby (SketchUp API)
- **前端**: Vue 3 + TypeScript + Vite
- **UI 风格**: macOS 设计规范
- **通信**: HtmlDialog + JavaScript 回调

### 开发模式

在开发模式下，可以使用 Vite 开发服务器：

```bash
cd frontend
npm run dev
```

然后修改 Ruby 代码中的 URL 为 `http://localhost:5173`

## 故障排除

### 常见问题

1. **插件加载错误 (ArgumentError: Unknown menu)**
   - 这通常是由于中文菜单名称编码问题导致的
   - 解决方案：插件已修改为使用英文菜单名称
   - 菜单位置：Tools → Material Browser

2. **插件未加载**
   - 检查插件文件是否正确放置在 Plugins 目录
   - 确保 SketchUp 版本兼容 (2017+)
   - 重启 SketchUp
   - 在Ruby控制台中运行测试：`load 'D:/path/to/plugin/test_load.rb'`

3. **界面显示异常**
   - 确保前端资源已正确构建
   - 检查 `frontend/dist` 目录是否存在
   - 重新运行 `npm run build`
   - 检查 `frontend/dist/index.html` 文件是否存在

4. **材质操作失败**
   - 确保选择了正确的对象
   - 检查材质是否存在
   - 查看 SketchUp 控制台错误信息

5. **编码问题**
   - 确保所有Ruby文件都包含 `# encoding: utf-8` 声明
   - 避免在菜单名称中使用中文字符

### 调试方法

1. **Ruby 调试**: 使用 SketchUp 的 Ruby 控制台
2. **前端调试**: 在对话框中按 F12 打开开发者工具
3. **日志查看**: 检查浏览器控制台和 Ruby 控制台的错误信息

## 更新日志

### v1.0.0
- 初始版本发布
- 基本材质浏览和管理功能
- macOS 风格界面设计
- 支持材质搜索和分类

## 许可证

本项目采用 MIT 许可证。

## 支持

如有问题或建议，请联系开发者或提交 Issue。
