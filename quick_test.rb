# encoding: utf-8
# 快速测试脚本 - 在SketchUp Ruby控制台中运行

puts "开始测试材质浏览器插件..."

begin
  # 测试基本的UI功能
  puts "1. 测试UI菜单创建..."
  menu = UI.menu("Tools")
  puts "   ✓ Tools菜单访问成功"
  
  # 测试HtmlDialog创建
  puts "2. 测试HtmlDialog创建..."
  dialog = UI::HtmlDialog.new({
    :dialog_title => "Test Dialog",
    :width => 400,
    :height => 300
  })
  puts "   ✓ HtmlDialog创建成功"
  
  # 测试材质访问
  puts "3. 测试材质系统访问..."
  model = Sketchup.active_model
  materials = model.materials
  puts "   ✓ 当前模型有 #{materials.length} 个材质"
  
  # 测试JSON功能
  puts "4. 测试JSON功能..."
  require 'json'
  test_data = { name: "test", color: [1, 0, 0] }
  json_string = test_data.to_json
  puts "   ✓ JSON序列化成功: #{json_string}"
  
  puts "\n✅ 所有基本功能测试通过！"
  puts "现在可以尝试加载完整插件："
  puts "load 'D:/115/材质浏览器/plugin/material_browser.rb'"
  
rescue => e
  puts "\n❌ 测试失败: #{e.message}"
  puts "错误详情: #{e.backtrace.first}"
end
