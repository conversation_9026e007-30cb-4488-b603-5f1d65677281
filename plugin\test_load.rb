# 简化的测试加载文件
# 用于验证插件是否能正常加载

require 'sketchup.rb'

module MaterialBrowserTest
  
  def self.test_dialog
    dialog = UI::HtmlDialog.new(
      {
        :dialog_title => "Material Browser Test",
        :preferences_key => "com.materialbrowser.test",
        :scrollable => false,
        :resizable => true,
        :width => 800,
        :height => 600,
        :style => UI::HtmlDialog::STYLE_DIALOG
      }
    )
    
    # 设置简单的HTML内容
    html_content = <<-HTML
    <!DOCTYPE html>
    <html>
    <head>
        <title>Material Browser Test</title>
        <style>
            body { 
                font-family: -apple-system, BlinkMacSystemFont, sans-serif; 
                padding: 20px; 
                background: #f5f5f7;
            }
            .container {
                background: white;
                padding: 20px;
                border-radius: 12px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            button {
                background: #007aff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                cursor: pointer;
                margin: 5px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Material Browser Test</h1>
            <p>插件加载成功！</p>
            <button onclick="testFunction()">测试按钮</button>
            <div id="output"></div>
        </div>
        
        <script>
            function testFunction() {
                document.getElementById('output').innerHTML = '<p>测试功能正常工作！</p>';
            }
            
            // 模拟材质数据
            const materials = [
                { name: 'Red', color: [1, 0, 0] },
                { name: 'Blue', color: [0, 0, 1] },
                { name: 'Green', color: [0, 1, 0] }
            ];
            
            console.log('Materials loaded:', materials);
        </script>
    </body>
    </html>
    HTML
    
    dialog.set_html(html_content)
    dialog.show
  end
  
  # 添加到Tools菜单
  unless file_loaded?(__FILE__)
    menu = UI.menu("Tools")
    menu.add_item("Test Material Browser") { MaterialBrowserTest.test_dialog }
    file_loaded(__FILE__)
  end
  
end

puts "Material Browser Test plugin loaded successfully!"
