<template>
  <div class="dialog-overlay" @click="$emit('close')">
    <div class="dialog" @click.stop>
      <div class="dialog-header">
        <h2>创建新材质</h2>
        <button class="close-btn" @click="$emit('close')">
          <svg viewBox="0 0 24 24" width="20" height="20">
            <path fill="currentColor" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
          </svg>
        </button>
      </div>
      
      <div class="dialog-content">
        <div class="form-group">
          <label for="materialName">材质名称</label>
          <input 
            id="materialName"
            v-model="materialData.name" 
            type="text" 
            class="input"
            placeholder="输入材质名称"
            required
          />
        </div>
        
        <div class="form-group">
          <label for="materialColor">颜色</label>
          <div class="color-input-group">
            <input 
              id="materialColor"
              v-model="colorHex" 
              type="color" 
              class="color-picker"
            />
            <input 
              v-model="colorHex" 
              type="text" 
              class="input color-text"
              placeholder="#ffffff"
            />
          </div>
        </div>
        
        <div class="form-group">
          <label for="materialAlpha">透明度</label>
          <div class="slider-group">
            <input 
              id="materialAlpha"
              v-model="materialData.alpha" 
              type="range" 
              min="0" 
              max="1" 
              step="0.01"
              class="slider"
            />
            <span class="slider-value">{{ Math.round(materialData.alpha * 100) }}%</span>
          </div>
        </div>
        
        <div class="form-group">
          <label>
            <input 
              v-model="materialData.use_alpha" 
              type="checkbox" 
              class="checkbox"
            />
            使用透明度
          </label>
        </div>
        
        <div class="preview-section">
          <h3>预览</h3>
          <div class="material-preview">
            <div 
              class="preview-swatch"
              :style="{ 
                backgroundColor: colorHex,
                opacity: materialData.use_alpha ? materialData.alpha : 1
              }"
            ></div>
          </div>
        </div>
      </div>
      
      <div class="dialog-footer">
        <button class="btn btn-secondary" @click="$emit('close')">取消</button>
        <button class="btn btn-primary" @click="handleCreate" :disabled="!materialData.name">
          创建
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

const emit = defineEmits<{
  close: []
  create: [data: any]
}>()

const materialData = ref({
  name: '',
  color: [1, 1, 1], // RGB值 (0-1)
  alpha: 1,
  use_alpha: false
})

const colorHex = ref('#ffffff')

// 监听颜色变化，转换为RGB
watch(colorHex, (newColor) => {
  const hex = newColor.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16) / 255
  const g = parseInt(hex.substr(2, 2), 16) / 255
  const b = parseInt(hex.substr(4, 2), 16) / 255
  materialData.value.color = [r, g, b]
})

// 监听RGB变化，转换为Hex
watch(() => materialData.value.color, (newColor) => {
  const [r, g, b] = newColor
  const hex = '#' + 
    Math.round(r * 255).toString(16).padStart(2, '0') +
    Math.round(g * 255).toString(16).padStart(2, '0') +
    Math.round(b * 255).toString(16).padStart(2, '0')
  colorHex.value = hex
}, { deep: true })

const handleCreate = () => {
  if (!materialData.value.name.trim()) {
    alert('请输入材质名称')
    return
  }
  
  emit('create', { ...materialData.value })
}
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.dialog {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e5ea;
}

.dialog-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8e8e93;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f2f2f7;
  color: #1d1d1f;
}

.dialog-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #1d1d1f;
}

.input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d1d6;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;
  outline: none;
}

.input:focus {
  border-color: #007aff;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.color-input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.color-picker {
  width: 50px;
  height: 40px;
  border: 1px solid #d1d1d6;
  border-radius: 8px;
  cursor: pointer;
  background: none;
  outline: none;
}

.color-text {
  flex: 1;
}

.slider-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #d1d1d6;
  outline: none;
  -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #007aff;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #007aff;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-value {
  font-size: 14px;
  font-weight: 500;
  color: #1d1d1f;
  min-width: 40px;
  text-align: right;
}

.checkbox {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  accent-color: #007aff;
}

.preview-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e5ea;
}

.preview-section h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
}

.material-preview {
  display: flex;
  justify-content: center;
}

.preview-swatch {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  border: 1px solid #e5e5ea;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e5ea;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  outline: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #007aff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056cc;
}

.btn-secondary {
  background: #f2f2f7;
  color: #1d1d1f;
  border: 1px solid #d1d1d6;
}

.btn-secondary:hover {
  background: #e5e5ea;
}
</style>
