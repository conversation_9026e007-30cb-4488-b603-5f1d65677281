# encoding: utf-8
# 重新加载插件的脚本

puts "正在重新加载材质浏览器插件..."

begin
  # 重新加载插件
  load 'D:/115/材质浏览器/plugin/material_browser.rb'
  puts "✅ 插件重新加载成功！"
  
  # 检查dist文件
  dist_path = 'D:/115/材质浏览器/frontend/dist/index.html'
  if File.exist?(dist_path)
    puts "✅ 找到前端构建文件: #{dist_path}"
  else
    puts "❌ 未找到前端构建文件，请运行 npm run build"
  end
  
  puts "\n现在可以通过以下方式打开材质浏览器："
  puts "1. 菜单: Tools → Material Browser"
  puts "2. 或者在控制台运行: MaterialBrowser.show_material_browser"
  
rescue => e
  puts "❌ 重新加载失败: #{e.message}"
  puts "错误详情: #{e.backtrace.first}"
end
