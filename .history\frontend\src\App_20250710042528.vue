<script setup lang="ts">
import { onMounted, ref } from 'vue'
import MaterialBrowser from './components/MaterialBrowser.vue'

// 声明全局函数供Ruby调用
declare global {
  interface Window {
    receiveMaterials: (materials: any[]) => void
    sketchup?: any
  }
}

const materials = ref<any[]>([])

// 接收来自Ruby的材质数据
window.receiveMaterials = (materialsData: any[]) => {
  materials.value = materialsData
}

// 向Ruby发送消息的辅助函数
const sendToRuby = (action: string, data?: any) => {
  if (window.sketchup) {
    window.sketchup.callback(action, data)
  } else {
    console.log('Ruby callback:', action, data)
  }
}

onMounted(() => {
  // 页面加载完成后请求材质数据
  sendToRuby('getMaterials')
})
</script>

<template>
  <div id="app">
    <MaterialBrowser
      :materials="materials"
      @apply-material="(name) => sendToRuby('applyMaterial', name)"
      @create-material="(data) => sendToRuby('createMaterial', data)"
      @delete-material="(name) => sendToRuby('deleteMaterial', name)"
      @import-material="() => sendToRuby('importMaterial')"
      @export-material="(name) => sendToRuby('exportMaterial', name)"
    />
  </div>
</template>

<style>
/* 全局样式已在main.css中定义 */
</style>
