<template>
  <div class="material-browser">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <div class="search-container">
          <input 
            v-model="searchQuery" 
            type="text" 
            placeholder="搜索材质..." 
            class="search-input"
          />
          <svg class="search-icon" viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
          </svg>
        </div>
      </div>
      
      <div class="toolbar-right">
        <button class="btn btn-secondary" @click="$emit('import-material')">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
          导入
        </button>
        <button class="btn btn-primary" @click="showCreateDialog = true">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
          </svg>
          新建
        </button>
      </div>
    </div>

    <!-- 分类导航 -->
    <div class="categories">
      <button 
        v-for="category in categories" 
        :key="category.id"
        :class="['category-btn', { active: selectedCategory === category.id }]"
        @click="selectedCategory = category.id"
      >
        {{ category.name }}
        <span class="category-count">{{ category.count }}</span>
      </button>
    </div>

    <!-- 材质网格 -->
    <div class="materials-grid">
      <MaterialCard
        v-for="material in filteredMaterials"
        :key="material.name"
        :material="material"
        @apply="$emit('apply-material', material.name)"
        @export="$emit('export-material', material.name)"
        @delete="$emit('delete-material', material.name)"
      />
    </div>

    <!-- 创建材质对话框 -->
    <CreateMaterialDialog
      v-if="showCreateDialog"
      @close="showCreateDialog = false"
      @create="handleCreateMaterial"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineEmits } from 'vue'
import MaterialCard from './MaterialCard.vue'
import CreateMaterialDialog from './CreateMaterialDialog.vue'

interface Material {
  name: string
  display_name: string
  color: number[]
  alpha: number
  texture: string | null
  use_alpha: boolean
}

interface Props {
  materials: Material[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'apply-material': [name: string]
  'create-material': [data: any]
  'delete-material': [name: string]
  'import-material': []
  'export-material': [name: string]
}>()

const searchQuery = ref('')
const selectedCategory = ref('all')
const showCreateDialog = ref(false)

// 分类数据
const categories = computed(() => [
  { id: 'all', name: '全部', count: props.materials.length },
  { id: 'textured', name: '纹理', count: props.materials.filter(m => m.texture).length },
  { id: 'solid', name: '纯色', count: props.materials.filter(m => !m.texture).length },
  { id: 'transparent', name: '透明', count: props.materials.filter(m => m.use_alpha).length }
])

// 过滤后的材质
const filteredMaterials = computed(() => {
  let filtered = props.materials

  // 按分类过滤
  if (selectedCategory.value !== 'all') {
    switch (selectedCategory.value) {
      case 'textured':
        filtered = filtered.filter(m => m.texture)
        break
      case 'solid':
        filtered = filtered.filter(m => !m.texture)
        break
      case 'transparent':
        filtered = filtered.filter(m => m.use_alpha)
        break
    }
  }

  // 按搜索关键词过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(m => 
      m.name.toLowerCase().includes(query) || 
      m.display_name.toLowerCase().includes(query)
    )
  }

  return filtered
})

const handleCreateMaterial = (materialData: any) => {
  emit('create-material', materialData)
  showCreateDialog.value = false
}
</script>

<style scoped>
.material-browser {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f7;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e5e5ea;
}

.toolbar-left {
  flex: 1;
}

.search-container {
  position: relative;
  max-width: 300px;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid #d1d1d6;
  border-radius: 20px;
  font-size: 14px;
  background: #f2f2f7;
  transition: all 0.2s ease;
}

.search-input:focus {
  background: white;
  border-color: #007aff;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #8e8e93;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.categories {
  display: flex;
  gap: 8px;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e5e5ea;
  overflow-x: auto;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #d1d1d6;
  border-radius: 20px;
  background: white;
  color: #1d1d1f;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.category-btn:hover {
  background: #f2f2f7;
}

.category-btn.active {
  background: #007aff;
  color: white;
  border-color: #007aff;
}

.category-count {
  background: rgba(0, 0, 0, 0.1);
  color: inherit;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
}

.category-btn.active .category-count {
  background: rgba(255, 255, 255, 0.2);
}

.materials-grid {
  flex: 1;
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  overflow-y: auto;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  outline: none;
}

.btn-primary {
  background: #007aff;
  color: white;
}

.btn-primary:hover {
  background: #0056cc;
}

.btn-secondary {
  background: #f2f2f7;
  color: #1d1d1f;
  border: 1px solid #d1d1d6;
}

.btn-secondary:hover {
  background: #e5e5ea;
}
</style>
