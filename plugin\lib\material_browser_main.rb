# encoding: utf-8
# 材质浏览器主要功能模块

require 'sketchup.rb'
require 'json'

module MaterialBrowser

  class MaterialBrowserDialog

    def initialize
      @dialog = nil
      @materials_data = []
      setup_dialog
    end

    # 设置对话框
    def setup_dialog
      @dialog = UI::HtmlDialog.new(
        {
          :dialog_title => "材质浏览器",
          :preferences_key => "com.materialbrowser.plugin",
          :scrollable => false,
          :resizable => true,
          :width => 1000,
          :height => 700,
          :left => 100,
          :top => 100,
          :min_width => 800,
          :min_height => 600,
          :max_width => 1400,
          :max_height => 1000,
          :style => UI::HtmlDialog::STYLE_DIALOG
        }
      )

      # 设置HTML文件路径
      html_file = File.join(File.dirname(__FILE__), '..', '..', 'frontend', 'dist', 'index.html')

      if File.exist?(html_file)
        puts "使用打包文件: #{html_file}"
        @dialog.set_file(html_file)
      else
        puts "找不到打包文件: #{html_file}"
        puts "请确保已运行 'npm run build' 构建前端资源"
        # 显示错误信息
        error_html = create_error_html("找不到前端资源文件，请运行 npm run build 构建项目")
        @dialog.set_html(error_html)
      end

      setup_callbacks
    end

    # 设置JavaScript回调
    def setup_callbacks
      # 设置sketchup对象供前端调用
      @dialog.execute_script("
        window.sketchup = {
          callback: function(action, data) {
            if (action === 'getMaterials') {
              sketchup.getMaterials();
            } else if (action === 'refreshMaterials') {
              sketchup.refreshMaterials();
            } else if (action === 'applyMaterial') {
              sketchup.applyMaterial(data);
            } else if (action === 'createMaterial') {
              sketchup.createMaterial(JSON.stringify(data));
            } else if (action === 'deleteMaterial') {
              sketchup.deleteMaterial(data);
            } else if (action === 'importMaterial') {
              sketchup.importMaterial();
            } else if (action === 'exportMaterial') {
              sketchup.exportMaterial(data);
            }
          }
        };
      ")

      # 获取材质列表
      @dialog.add_action_callback("getMaterials") do |action_context|
        materials = get_materials_data
        @dialog.execute_script("window.receiveMaterials && window.receiveMaterials(#{materials.to_json})")
      end

      # 刷新材质列表
      @dialog.add_action_callback("refreshMaterials") do |action_context|
        materials = get_materials_data
        @dialog.execute_script("window.receiveMaterials && window.receiveMaterials(#{materials.to_json})")
      end

      # 应用材质到选中的面
      @dialog.add_action_callback("applyMaterial") do |action_context, material_name|
        apply_material_to_selection(material_name)
      end

      # 创建新材质
      @dialog.add_action_callback("createMaterial") do |action_context, material_data_json|
        material_data = JSON.parse(material_data_json)
        create_material(material_data)
      end

      # 删除材质
      @dialog.add_action_callback("deleteMaterial") do |action_context, material_name|
        delete_material(material_name)
      end

      # 导入材质
      @dialog.add_action_callback("importMaterial") do |action_context|
        import_material
      end

      # 导出材质
      @dialog.add_action_callback("exportMaterial") do |action_context, material_name|
        export_material(material_name)
      end
    end

    # 显示对话框
    def show
      @dialog.show
    end

    # 创建错误页面HTML
    def create_error_html(message)
      <<-HTML
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>材质浏览器 - 错误</title>
          <style>
              body {
                  margin: 0;
                  padding: 40px;
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                  background: #f5f5f7;
                  color: #1d1d1f;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  min-height: 100vh;
              }
              .error-container {
                  background: white;
                  border-radius: 16px;
                  padding: 40px;
                  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                  text-align: center;
                  max-width: 500px;
              }
              .error-icon {
                  font-size: 48px;
                  margin-bottom: 20px;
              }
              h1 {
                  color: #ff3b30;
                  margin-bottom: 16px;
                  font-size: 24px;
              }
              p {
                  color: #666;
                  line-height: 1.5;
                  margin-bottom: 24px;
              }
              .instructions {
                  background: #f2f2f7;
                  padding: 20px;
                  border-radius: 12px;
                  text-align: left;
                  margin-top: 20px;
              }
              code {
                  background: #e5e5ea;
                  padding: 2px 6px;
                  border-radius: 4px;
                  font-family: 'SF Mono', Monaco, monospace;
              }
          </style>
      </head>
      <body>
          <div class="error-container">
              <div class="error-icon">⚠️</div>
              <h1>加载错误</h1>
              <p>#{message}</p>
              <div class="instructions">
                  <h3>解决步骤：</h3>
                  <ol>
                      <li>打开命令行，进入项目的 <code>frontend</code> 目录</li>
                      <li>运行 <code>npm install</code> 安装依赖</li>
                      <li>运行 <code>npm run build</code> 构建项目</li>
                      <li>重新打开材质浏览器</li>
                  </ol>
              </div>
          </div>
      </body>
      </html>
      HTML
    end

    # 获取材质数据
    def get_materials_data
      materials = []
      model = Sketchup.active_model

      model.materials.each do |material|
        # 获取材质的基本信息
        material_data = {
          name: material.name,
          display_name: material.display_name.empty? ? material.name : material.display_name,
          color: material.color.to_a[0..2], # RGB值 (0-1)
          alpha: material.alpha,
          use_alpha: material.use_alpha?,
          texture: nil,
          texture_path: nil,
          icon_data: nil
        }

        # 处理纹理信息
        if material.texture
          texture = material.texture
          material_data[:texture] = texture.filename
          material_data[:texture_path] = texture.filename

          # 获取纹理尺寸信息
          if texture.width > 0 && texture.height > 0
            material_data[:texture_width] = texture.width
            material_data[:texture_height] = texture.height
          end
        end

        # 生成材质预览图标的Base64数据
        material_data[:icon_data] = generate_material_icon(material)

        materials << material_data
      end

      puts "获取到 #{materials.length} 个材质"
      materials
    end

    # 生成材质图标的Base64数据
    def generate_material_icon(material)
      begin
        # 创建一个临时的材质预览
        # 这里我们创建一个简单的颜色块作为图标
        color = material.color
        r = (color.red * 255).to_i
        g = (color.green * 255).to_i
        b = (color.blue * 255).to_i

        # 如果有纹理，尝试获取纹理的缩略图
        if material.texture && material.texture.filename
          texture_path = material.texture.filename
          if File.exist?(texture_path)
            # 返回纹理文件的路径，前端可以直接使用
            return "file:///" + texture_path.gsub("\\", "/")
          end
        end

        # 如果没有纹理，返回颜色信息
        return "color:rgb(#{r},#{g},#{b})"

      rescue => e
        puts "生成材质图标失败: #{e.message}"
        # 返回默认颜色
        return "color:rgb(128,128,128)"
      end
    end

    # 应用材质到选中对象
    def apply_material_to_selection(material_name)
      model = Sketchup.active_model
      material = model.materials[material_name]

      return unless material

      selection = model.selection
      if selection.empty?
        UI.messagebox("请先选择要应用材质的对象")
        return
      end

      model.start_operation("应用材质", true)

      selection.each do |entity|
        if entity.respond_to?(:material=)
          entity.material = material
        elsif entity.is_a?(Sketchup::Group) || entity.is_a?(Sketchup::ComponentInstance)
          entity.material = material
        end
      end

      model.commit_operation
      UI.messagebox("材质已应用到选中对象")
    end

    # 创建新材质
    def create_material(material_data)
      model = Sketchup.active_model
      materials = model.materials

      material = materials.add(material_data['name'])
      material.color = material_data['color'] if material_data['color']
      material.alpha = material_data['alpha'] if material_data['alpha']

      # 刷新材质列表
      refresh_materials
    end

    # 删除材质
    def delete_material(material_name)
      model = Sketchup.active_model
      material = model.materials[material_name]

      if material
        model.materials.remove(material)
        refresh_materials
        UI.messagebox("材质 '#{material_name}' 已删除")
      end
    end

    # 导入材质
    def import_material
      # 打开文件选择对话框
      file_path = UI.openpanel("选择材质文件", "", "材质文件|*.skm;*.jpg;*.png;*.bmp||")

      return unless file_path

      model = Sketchup.active_model
      materials = model.materials

      begin
        if File.extname(file_path).downcase == '.skm'
          # SketchUp材质文件
          material = materials.load(file_path)
        else
          # 图片文件
          material_name = File.basename(file_path, File.extname(file_path))
          material = materials.add(material_name)
          material.texture = file_path
        end

        refresh_materials
        UI.messagebox("材质导入成功")
      rescue => e
        UI.messagebox("导入失败: #{e.message}")
      end
    end

    # 导出材质
    def export_material(material_name)
      model = Sketchup.active_model
      material = model.materials[material_name]

      return unless material

      # 选择保存位置
      file_path = UI.savepanel("导出材质", "", "#{material_name}.skm")

      return unless file_path

      begin
        material.save_as(file_path)
        UI.messagebox("材质导出成功")
      rescue => e
        UI.messagebox("导出失败: #{e.message}")
      end
    end

    # 刷新材质列表
    def refresh_materials
      materials = get_materials_data
      @dialog.execute_script("window.receiveMaterials(#{materials.to_json})")
    end

  end

  # 全局实例
  @@dialog_instance = nil

  # 显示材质浏览器
  def self.show_material_browser
    @@dialog_instance ||= MaterialBrowserDialog.new
    @@dialog_instance.show
  end

  # 添加菜单项
  unless file_loaded?(__FILE__)
    # 使用Tools菜单避免编码问题
    menu = UI.menu("Tools")
    menu.add_item("Material Browser") { MaterialBrowser.show_material_browser }

    # 添加工具栏
    toolbar = UI::Toolbar.new("Material Browser")
    cmd = UI::Command.new("Material Browser") { MaterialBrowser.show_material_browser }
    cmd.tooltip = "Open Material Browser"
    cmd.status_bar_text = "Browse and manage SketchUp materials"
    cmd.small_icon = File.join(File.dirname(__FILE__), "icons", "material_browser_16.png")
    cmd.large_icon = File.join(File.dirname(__FILE__), "icons", "material_browser_24.png")
    toolbar.add_item(cmd)
    toolbar.show

    file_loaded(__FILE__)
  end

end
