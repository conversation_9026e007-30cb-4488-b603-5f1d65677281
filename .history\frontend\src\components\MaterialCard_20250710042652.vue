<template>
  <div class="material-card" @click="$emit('apply')">
    <div class="material-preview">
      <div 
        v-if="material.texture"
        class="texture-preview"
        :style="{ backgroundImage: `url(${material.texture})` }"
      ></div>
      <div 
        v-else
        class="color-preview"
        :style="{ backgroundColor: colorString }"
      ></div>
      
      <!-- 透明度指示器 -->
      <div v-if="material.use_alpha" class="alpha-indicator">
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path fill="white" d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z"/>
        </svg>
      </div>
    </div>
    
    <div class="material-info">
      <h3 class="material-name">{{ material.display_name || material.name }}</h3>
      <div class="material-details">
        <span v-if="material.texture" class="detail-tag texture-tag">纹理</span>
        <span v-if="material.use_alpha" class="detail-tag alpha-tag">透明</span>
        <span class="detail-tag color-tag">{{ colorString }}</span>
      </div>
    </div>
    
    <div class="material-actions" @click.stop>
      <button class="action-btn" @click="$emit('apply')" title="应用材质">
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
        </svg>
      </button>
      
      <div class="dropdown" ref="dropdownRef">
        <button class="action-btn dropdown-toggle" @click="showDropdown = !showDropdown" title="更多操作">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z"/>
          </svg>
        </button>
        
        <div v-if="showDropdown" class="dropdown-menu">
          <button class="dropdown-item" @click="handleExport">
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path fill="currentColor" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
            </svg>
            导出
          </button>
          <button class="dropdown-item delete" @click="handleDelete">
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path fill="currentColor" d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
            </svg>
            删除
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface Material {
  name: string
  display_name: string
  color: number[]
  alpha: number
  texture: string | null
  use_alpha: boolean
}

interface Props {
  material: Material
}

const props = defineProps<Props>()

const emit = defineEmits<{
  apply: []
  export: []
  delete: []
}>()

const showDropdown = ref(false)
const dropdownRef = ref<HTMLElement>()

// 颜色字符串
const colorString = computed(() => {
  const [r, g, b] = props.material.color
  return `rgb(${Math.round(r * 255)}, ${Math.round(g * 255)}, ${Math.round(b * 255)})`
})

// 处理导出
const handleExport = () => {
  emit('export')
  showDropdown.value = false
}

// 处理删除
const handleDelete = () => {
  if (confirm(`确定要删除材质 "${props.material.display_name || props.material.name}" 吗？`)) {
    emit('delete')
  }
  showDropdown.value = false
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event: MouseEvent) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    showDropdown.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.material-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
  overflow: hidden;
  position: relative;
}

.material-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.material-preview {
  position: relative;
  width: 100%;
  height: 120px;
  overflow: hidden;
}

.texture-preview {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.color-preview {
  width: 100%;
  height: 100%;
}

.alpha-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.material-info {
  padding: 12px 16px;
}

.material-name {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.material-details {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.detail-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.texture-tag {
  background: #e3f2fd;
  color: #1976d2;
}

.alpha-tag {
  background: #f3e5f5;
  color: #7b1fa2;
}

.color-tag {
  background: #f5f5f5;
  color: #666;
}

.material-actions {
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.material-card:hover .material-actions {
  opacity: 1;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  color: #1d1d1f;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.action-btn:hover {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  min-width: 120px;
  z-index: 1000;
}

.dropdown-item {
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: none;
  color: #1d1d1f;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background: #f2f2f7;
}

.dropdown-item.delete {
  color: #ff3b30;
}

.dropdown-item.delete:hover {
  background: #ffebee;
}
</style>
