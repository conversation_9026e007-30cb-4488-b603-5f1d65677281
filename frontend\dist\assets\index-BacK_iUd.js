const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./AboutView-DB32AxCe.js","./AboutView-CSIvawM9.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function xs(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const J={},Pt=[],Fe=()=>{},ai=()=>!1,In=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Cs=e=>e.startsWith("onUpdate:"),pe=Object.assign,Es=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},fi=Object.prototype.hasOwnProperty,q=(e,t)=>fi.call(e,t),V=Array.isArray,Ot=e=>gn(e)==="[object Map]",Hn=e=>gn(e)==="[object Set]",Ks=e=>gn(e)==="[object Date]",N=e=>typeof e=="function",ie=e=>typeof e=="string",Oe=e=>typeof e=="symbol",se=e=>e!==null&&typeof e=="object",Nr=e=>(se(e)||N(e))&&N(e.then)&&N(e.catch),Fr=Object.prototype.toString,gn=e=>Fr.call(e),di=e=>gn(e).slice(8,-1),Br=e=>gn(e)==="[object Object]",Ss=e=>ie(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Zt=xs(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Ln=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},hi=/-(\w)/g,ft=Ln(e=>e.replace(hi,(t,n)=>n?n.toUpperCase():"")),pi=/\B([A-Z])/g,wt=Ln(e=>e.replace(pi,"-$1").toLowerCase()),Ur=Ln(e=>e.charAt(0).toUpperCase()+e.slice(1)),qn=Ln(e=>e?`on${Ur(e)}`:""),at=(e,t)=>!Object.is(e,t),xn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ls=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},cs=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Ws;const kn=()=>Ws||(Ws=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function bt(e){if(V(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=ie(s)?yi(s):bt(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(ie(e)||se(e))return e}const gi=/;(?![^(]*\))/g,mi=/:([^]+)/,vi=/\/\*[^]*?\*\//g;function yi(e){const t={};return e.replace(vi,"").split(gi).forEach(n=>{if(n){const s=n.split(mi);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function zn(e){let t="";if(ie(e))t=e;else if(V(e))for(let n=0;n<e.length;n++){const s=zn(e[n]);s&&(t+=s+" ")}else if(se(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const bi="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",_i=xs(bi);function Kr(e){return!!e||e===""}function wi(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Vn(e[s],t[s]);return n}function Vn(e,t){if(e===t)return!0;let n=Ks(e),s=Ks(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=Oe(e),s=Oe(t),n||s)return e===t;if(n=V(e),s=V(t),n||s)return n&&s?wi(e,t):!1;if(n=se(e),s=se(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,o=Object.keys(t).length;if(r!==o)return!1;for(const i in e){const l=e.hasOwnProperty(i),c=t.hasOwnProperty(i);if(l&&!c||!l&&c||!Vn(e[i],t[i]))return!1}}return String(e)===String(t)}function Wr(e,t){return e.findIndex(n=>Vn(n,t))}const qr=e=>!!(e&&e.__v_isRef===!0),Vt=e=>ie(e)?e:e==null?"":V(e)||se(e)&&(e.toString===Fr||!N(e.toString))?qr(e)?Vt(e.value):JSON.stringify(e,Gr,2):String(e),Gr=(e,t)=>qr(t)?Gr(e,t.value):Ot(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[Gn(s,o)+" =>"]=r,n),{})}:Hn(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Gn(n))}:Oe(t)?Gn(t):se(t)&&!V(t)&&!Br(t)?String(t):t,Gn=(e,t="")=>{var n;return Oe(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let xe;class Yr{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=xe,!t&&xe&&(this.index=(xe.scopes||(xe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=xe;try{return xe=this,t()}finally{xe=n}}}on(){++this._on===1&&(this.prevScope=xe,xe=this)}off(){this._on>0&&--this._on===0&&(xe=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function xi(e){return new Yr(e)}function Ci(){return xe}let te;const Yn=new WeakSet;class Qr{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,xe&&xe.active&&xe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Yn.has(this)&&(Yn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Jr(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,qs(this),Xr(this);const t=te,n=Pe;te=this,Pe=!0;try{return this.fn()}finally{eo(this),te=t,Pe=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ms(t);this.deps=this.depsTail=void 0,qs(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Yn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){us(this)&&this.run()}get dirty(){return us(this)}}let Zr=0,Jt,Xt;function Jr(e,t=!1){if(e.flags|=8,t){e.next=Xt,Xt=e;return}e.next=Jt,Jt=e}function Rs(){Zr++}function As(){if(--Zr>0)return;if(Xt){let t=Xt;for(Xt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Jt;){let t=Jt;for(Jt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Xr(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function eo(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),Ms(s),Ei(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function us(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(to(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function to(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===on)||(e.globalVersion=on,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!us(e))))return;e.flags|=2;const t=e.dep,n=te,s=Pe;te=e,Pe=!0;try{Xr(e);const r=e.fn(e._value);(t.version===0||at(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{te=n,Pe=s,eo(e),e.flags&=-3}}function Ms(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Ms(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Ei(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Pe=!0;const no=[];function Je(){no.push(Pe),Pe=!1}function Xe(){const e=no.pop();Pe=e===void 0?!0:e}function qs(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=te;te=void 0;try{t()}finally{te=n}}}let on=0;class Si{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ps{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!te||!Pe||te===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==te)n=this.activeLink=new Si(te,this),te.deps?(n.prevDep=te.depsTail,te.depsTail.nextDep=n,te.depsTail=n):te.deps=te.depsTail=n,so(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=te.depsTail,n.nextDep=void 0,te.depsTail.nextDep=n,te.depsTail=n,te.deps===n&&(te.deps=s)}return n}trigger(t){this.version++,on++,this.notify(t)}notify(t){Rs();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{As()}}}function so(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)so(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const as=new WeakMap,_t=Symbol(""),fs=Symbol(""),ln=Symbol("");function fe(e,t,n){if(Pe&&te){let s=as.get(e);s||as.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new Ps),r.map=s,r.key=n),r.track()}}function Ye(e,t,n,s,r,o){const i=as.get(e);if(!i){on++;return}const l=c=>{c&&c.trigger()};if(Rs(),t==="clear")i.forEach(l);else{const c=V(e),h=c&&Ss(n);if(c&&n==="length"){const u=Number(s);i.forEach((a,p)=>{(p==="length"||p===ln||!Oe(p)&&p>=u)&&l(a)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),h&&l(i.get(ln)),t){case"add":c?h&&l(i.get("length")):(l(i.get(_t)),Ot(e)&&l(i.get(fs)));break;case"delete":c||(l(i.get(_t)),Ot(e)&&l(i.get(fs)));break;case"set":Ot(e)&&l(i.get(_t));break}}As()}function St(e){const t=W(e);return t===e?t:(fe(t,"iterate",ln),Me(e)?t:t.map(ue))}function jn(e){return fe(e=W(e),"iterate",ln),e}const Ri={__proto__:null,[Symbol.iterator](){return Qn(this,Symbol.iterator,ue)},concat(...e){return St(this).concat(...e.map(t=>V(t)?St(t):t))},entries(){return Qn(this,"entries",e=>(e[1]=ue(e[1]),e))},every(e,t){return We(this,"every",e,t,void 0,arguments)},filter(e,t){return We(this,"filter",e,t,n=>n.map(ue),arguments)},find(e,t){return We(this,"find",e,t,ue,arguments)},findIndex(e,t){return We(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return We(this,"findLast",e,t,ue,arguments)},findLastIndex(e,t){return We(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return We(this,"forEach",e,t,void 0,arguments)},includes(...e){return Zn(this,"includes",e)},indexOf(...e){return Zn(this,"indexOf",e)},join(e){return St(this).join(e)},lastIndexOf(...e){return Zn(this,"lastIndexOf",e)},map(e,t){return We(this,"map",e,t,void 0,arguments)},pop(){return Ut(this,"pop")},push(...e){return Ut(this,"push",e)},reduce(e,...t){return Gs(this,"reduce",e,t)},reduceRight(e,...t){return Gs(this,"reduceRight",e,t)},shift(){return Ut(this,"shift")},some(e,t){return We(this,"some",e,t,void 0,arguments)},splice(...e){return Ut(this,"splice",e)},toReversed(){return St(this).toReversed()},toSorted(e){return St(this).toSorted(e)},toSpliced(...e){return St(this).toSpliced(...e)},unshift(...e){return Ut(this,"unshift",e)},values(){return Qn(this,"values",ue)}};function Qn(e,t,n){const s=jn(e),r=s[t]();return s!==e&&!Me(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const Ai=Array.prototype;function We(e,t,n,s,r,o){const i=jn(e),l=i!==e&&!Me(e),c=i[t];if(c!==Ai[t]){const a=c.apply(e,o);return l?ue(a):a}let h=n;i!==e&&(l?h=function(a,p){return n.call(this,ue(a),p,e)}:n.length>2&&(h=function(a,p){return n.call(this,a,p,e)}));const u=c.call(i,h,s);return l&&r?r(u):u}function Gs(e,t,n,s){const r=jn(e);let o=n;return r!==e&&(Me(e)?n.length>3&&(o=function(i,l,c){return n.call(this,i,l,c,e)}):o=function(i,l,c){return n.call(this,i,ue(l),c,e)}),r[t](o,...s)}function Zn(e,t,n){const s=W(e);fe(s,"iterate",ln);const r=s[t](...n);return(r===-1||r===!1)&&$s(n[0])?(n[0]=W(n[0]),s[t](...n)):r}function Ut(e,t,n=[]){Je(),Rs();const s=W(e)[t].apply(e,n);return As(),Xe(),s}const Mi=xs("__proto__,__v_isRef,__isVue"),ro=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Oe));function Pi(e){Oe(e)||(e=String(e));const t=W(this);return fe(t,"has",e),t.hasOwnProperty(e)}class oo{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?ji:uo:o?co:lo).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=V(t);if(!r){let c;if(i&&(c=Ri[n]))return c;if(n==="hasOwnProperty")return Pi}const l=Reflect.get(t,n,he(t)?t:s);return(Oe(n)?ro.has(n):Mi(n))||(r||fe(t,"get",n),o)?l:he(l)?i&&Ss(n)?l:l.value:se(l)?r?fo(l):Dn(l):l}}class io extends oo{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const c=dt(o);if(!Me(s)&&!dt(s)&&(o=W(o),s=W(s)),!V(t)&&he(o)&&!he(s))return c?!1:(o.value=s,!0)}const i=V(t)&&Ss(n)?Number(n)<t.length:q(t,n),l=Reflect.set(t,n,s,he(t)?t:r);return t===W(r)&&(i?at(s,o)&&Ye(t,"set",n,s):Ye(t,"add",n,s)),l}deleteProperty(t,n){const s=q(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&Ye(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!Oe(n)||!ro.has(n))&&fe(t,"has",n),s}ownKeys(t){return fe(t,"iterate",V(t)?"length":_t),Reflect.ownKeys(t)}}class Oi extends oo{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Ti=new io,$i=new Oi,Ii=new io(!0);const ds=e=>e,bn=e=>Reflect.getPrototypeOf(e);function Hi(e,t,n){return function(...s){const r=this.__v_raw,o=W(r),i=Ot(o),l=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,h=r[e](...s),u=n?ds:t?Rn:ue;return!t&&fe(o,"iterate",c?fs:_t),{next(){const{value:a,done:p}=h.next();return p?{value:a,done:p}:{value:l?[u(a[0]),u(a[1])]:u(a),done:p}},[Symbol.iterator](){return this}}}}function _n(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Li(e,t){const n={get(r){const o=this.__v_raw,i=W(o),l=W(r);e||(at(r,l)&&fe(i,"get",r),fe(i,"get",l));const{has:c}=bn(i),h=t?ds:e?Rn:ue;if(c.call(i,r))return h(o.get(r));if(c.call(i,l))return h(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&fe(W(r),"iterate",_t),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=W(o),l=W(r);return e||(at(r,l)&&fe(i,"has",r),fe(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,c=W(l),h=t?ds:e?Rn:ue;return!e&&fe(c,"iterate",_t),l.forEach((u,a)=>r.call(o,h(u),h(a),i))}};return pe(n,e?{add:_n("add"),set:_n("set"),delete:_n("delete"),clear:_n("clear")}:{add(r){!t&&!Me(r)&&!dt(r)&&(r=W(r));const o=W(this);return bn(o).has.call(o,r)||(o.add(r),Ye(o,"add",r,r)),this},set(r,o){!t&&!Me(o)&&!dt(o)&&(o=W(o));const i=W(this),{has:l,get:c}=bn(i);let h=l.call(i,r);h||(r=W(r),h=l.call(i,r));const u=c.call(i,r);return i.set(r,o),h?at(o,u)&&Ye(i,"set",r,o):Ye(i,"add",r,o),this},delete(r){const o=W(this),{has:i,get:l}=bn(o);let c=i.call(o,r);c||(r=W(r),c=i.call(o,r)),l&&l.call(o,r);const h=o.delete(r);return c&&Ye(o,"delete",r,void 0),h},clear(){const r=W(this),o=r.size!==0,i=r.clear();return o&&Ye(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=Hi(r,e,t)}),n}function Os(e,t){const n=Li(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(q(n,r)&&r in s?n:s,r,o)}const ki={get:Os(!1,!1)},zi={get:Os(!1,!0)},Vi={get:Os(!0,!1)};const lo=new WeakMap,co=new WeakMap,uo=new WeakMap,ji=new WeakMap;function Di(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ni(e){return e.__v_skip||!Object.isExtensible(e)?0:Di(di(e))}function Dn(e){return dt(e)?e:Ts(e,!1,Ti,ki,lo)}function ao(e){return Ts(e,!1,Ii,zi,co)}function fo(e){return Ts(e,!0,$i,Vi,uo)}function Ts(e,t,n,s,r){if(!se(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=Ni(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?s:n);return r.set(e,l),l}function Tt(e){return dt(e)?Tt(e.__v_raw):!!(e&&e.__v_isReactive)}function dt(e){return!!(e&&e.__v_isReadonly)}function Me(e){return!!(e&&e.__v_isShallow)}function $s(e){return e?!!e.__v_raw:!1}function W(e){const t=e&&e.__v_raw;return t?W(t):e}function ho(e){return!q(e,"__v_skip")&&Object.isExtensible(e)&&ls(e,"__v_skip",!0),e}const ue=e=>se(e)?Dn(e):e,Rn=e=>se(e)?fo(e):e;function he(e){return e?e.__v_isRef===!0:!1}function Be(e){return po(e,!1)}function Fi(e){return po(e,!0)}function po(e,t){return he(e)?e:new Bi(e,t)}class Bi{constructor(t,n){this.dep=new Ps,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:W(t),this._value=n?t:ue(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Me(t)||dt(t);t=s?t:W(t),at(t,n)&&(this._rawValue=t,this._value=s?t:ue(t),this.dep.trigger())}}function $t(e){return he(e)?e.value:e}const Ui={get:(e,t,n)=>t==="__v_raw"?e:$t(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return he(r)&&!he(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function go(e){return Tt(e)?e:new Proxy(e,Ui)}class Ki{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Ps(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=on-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&te!==this)return Jr(this,!0),!0}get value(){const t=this.dep.track();return to(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Wi(e,t,n=!1){let s,r;return N(e)?s=e:(s=e.get,r=e.set),new Ki(s,r,n)}const wn={},An=new WeakMap;let vt;function qi(e,t=!1,n=vt){if(n){let s=An.get(n);s||An.set(n,s=[]),s.push(e)}}function Gi(e,t,n=J){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:l,call:c}=n,h=T=>r?T:Me(T)||r===!1||r===0?Qe(T,1):Qe(T);let u,a,p,m,R=!1,P=!1;if(he(e)?(a=()=>e.value,R=Me(e)):Tt(e)?(a=()=>h(e),R=!0):V(e)?(P=!0,R=e.some(T=>Tt(T)||Me(T)),a=()=>e.map(T=>{if(he(T))return T.value;if(Tt(T))return h(T);if(N(T))return c?c(T,2):T()})):N(e)?t?a=c?()=>c(e,2):e:a=()=>{if(p){Je();try{p()}finally{Xe()}}const T=vt;vt=u;try{return c?c(e,3,[m]):e(m)}finally{vt=T}}:a=Fe,t&&r){const T=a,Q=r===!0?1/0:r;a=()=>Qe(T(),Q)}const j=Ci(),k=()=>{u.stop(),j&&j.active&&Es(j.effects,u)};if(o&&t){const T=t;t=(...Q)=>{T(...Q),k()}}let I=P?new Array(e.length).fill(wn):wn;const z=T=>{if(!(!(u.flags&1)||!u.dirty&&!T))if(t){const Q=u.run();if(r||R||(P?Q.some((ce,re)=>at(ce,I[re])):at(Q,I))){p&&p();const ce=vt;vt=u;try{const re=[Q,I===wn?void 0:P&&I[0]===wn?[]:I,m];I=Q,c?c(t,3,re):t(...re)}finally{vt=ce}}}else u.run()};return l&&l(z),u=new Qr(a),u.scheduler=i?()=>i(z,!1):z,m=T=>qi(T,!1,u),p=u.onStop=()=>{const T=An.get(u);if(T){if(c)c(T,4);else for(const Q of T)Q();An.delete(u)}},t?s?z(!0):I=u.run():i?i(z.bind(null,!0),!0):u.run(),k.pause=u.pause.bind(u),k.resume=u.resume.bind(u),k.stop=k,k}function Qe(e,t=1/0,n){if(t<=0||!se(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,he(e))Qe(e.value,t,n);else if(V(e))for(let s=0;s<e.length;s++)Qe(e[s],t,n);else if(Hn(e)||Ot(e))e.forEach(s=>{Qe(s,t,n)});else if(Br(e)){for(const s in e)Qe(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&Qe(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function mn(e,t,n,s){try{return s?e(...s):e()}catch(r){Nn(r,t,n)}}function Ue(e,t,n,s){if(N(e)){const r=mn(e,t,n,s);return r&&Nr(r)&&r.catch(o=>{Nn(o,t,n)}),r}if(V(e)){const r=[];for(let o=0;o<e.length;o++)r.push(Ue(e[o],t,n,s));return r}}function Nn(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||J;if(t){let l=t.parent;const c=t.proxy,h=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const u=l.ec;if(u){for(let a=0;a<u.length;a++)if(u[a](e,c,h)===!1)return}l=l.parent}if(o){Je(),mn(o,null,10,[e,c,h]),Xe();return}}Yi(e,n,r,s,i)}function Yi(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const me=[];let De=-1;const It=[];let lt=null,Rt=0;const mo=Promise.resolve();let Mn=null;function vo(e){const t=Mn||mo;return e?t.then(this?e.bind(this):e):t}function Qi(e){let t=De+1,n=me.length;for(;t<n;){const s=t+n>>>1,r=me[s],o=cn(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function Is(e){if(!(e.flags&1)){const t=cn(e),n=me[me.length-1];!n||!(e.flags&2)&&t>=cn(n)?me.push(e):me.splice(Qi(t),0,e),e.flags|=1,yo()}}function yo(){Mn||(Mn=mo.then(_o))}function Zi(e){V(e)?It.push(...e):lt&&e.id===-1?lt.splice(Rt+1,0,e):e.flags&1||(It.push(e),e.flags|=1),yo()}function Ys(e,t,n=De+1){for(;n<me.length;n++){const s=me[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;me.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function bo(e){if(It.length){const t=[...new Set(It)].sort((n,s)=>cn(n)-cn(s));if(It.length=0,lt){lt.push(...t);return}for(lt=t,Rt=0;Rt<lt.length;Rt++){const n=lt[Rt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}lt=null,Rt=0}}const cn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function _o(e){try{for(De=0;De<me.length;De++){const t=me[De];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),mn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;De<me.length;De++){const t=me[De];t&&(t.flags&=-2)}De=-1,me.length=0,bo(),Mn=null,(me.length||It.length)&&_o()}}let de=null,wo=null;function Pn(e){const t=de;return de=e,wo=e&&e.type.__scopeId||null,t}function ae(e,t=de,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&or(-1);const o=Pn(t);let i;try{i=e(...r)}finally{Pn(o),s._d&&or(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function At(e,t){if(de===null)return e;const n=Kn(de),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,c=J]=t[r];o&&(N(o)&&(o={mounted:o,updated:o}),o.deep&&Qe(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:c}))}return e}function gt(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let c=l.dir[s];c&&(Je(),Ue(c,n,8,[e.el,l,e,t]),Xe())}}const Ji=Symbol("_vte"),Xi=e=>e.__isTeleport;function Hs(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Hs(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function ht(e,t){return N(e)?pe({name:e.name},t,{setup:e}):e}function xo(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function en(e,t,n,s,r=!1){if(V(e)){e.forEach((R,P)=>en(R,t&&(V(t)?t[P]:t),n,s,r));return}if(Ht(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&en(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?Kn(s.component):s.el,i=r?null:o,{i:l,r:c}=e,h=t&&t.r,u=l.refs===J?l.refs={}:l.refs,a=l.setupState,p=W(a),m=a===J?()=>!1:R=>q(p,R);if(h!=null&&h!==c&&(ie(h)?(u[h]=null,m(h)&&(a[h]=null)):he(h)&&(h.value=null)),N(c))mn(c,l,12,[i,u]);else{const R=ie(c),P=he(c);if(R||P){const j=()=>{if(e.f){const k=R?m(c)?a[c]:u[c]:c.value;r?V(k)&&Es(k,o):V(k)?k.includes(o)||k.push(o):R?(u[c]=[o],m(c)&&(a[c]=u[c])):(c.value=[o],e.k&&(u[e.k]=c.value))}else R?(u[c]=i,m(c)&&(a[c]=i)):P&&(c.value=i,e.k&&(u[e.k]=i))};i?(j.id=-1,Se(j,n)):j()}}}kn().requestIdleCallback;kn().cancelIdleCallback;const Ht=e=>!!e.type.__asyncLoader,Co=e=>e.type.__isKeepAlive;function el(e,t){Eo(e,"a",t)}function tl(e,t){Eo(e,"da",t)}function Eo(e,t,n=ye){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Fn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)Co(r.parent.vnode)&&nl(s,t,n,r),r=r.parent}}function nl(e,t,n,s){const r=Fn(t,e,s,!0);ks(()=>{Es(s[t],r)},n)}function Fn(e,t,n=ye,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{Je();const l=vn(n),c=Ue(t,n,e,i);return l(),Xe(),c});return s?r.unshift(o):r.push(o),o}}const tt=e=>(t,n=ye)=>{(!dn||e==="sp")&&Fn(e,(...s)=>t(...s),n)},sl=tt("bm"),Ls=tt("m"),rl=tt("bu"),ol=tt("u"),il=tt("bum"),ks=tt("um"),ll=tt("sp"),cl=tt("rtg"),ul=tt("rtc");function al(e,t=ye){Fn("ec",e,t)}const fl=Symbol.for("v-ndc");function Qs(e,t,n,s){let r;const o=n,i=V(e);if(i||ie(e)){const l=i&&Tt(e);let c=!1,h=!1;l&&(c=!Me(e),h=dt(e),e=jn(e)),r=new Array(e.length);for(let u=0,a=e.length;u<a;u++)r[u]=t(c?h?Rn(ue(e[u])):ue(e[u]):e[u],u,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,o)}else if(se(e))if(e[Symbol.iterator])r=Array.from(e,(l,c)=>t(l,c,void 0,o));else{const l=Object.keys(e);r=new Array(l.length);for(let c=0,h=l.length;c<h;c++){const u=l[c];r[c]=t(e[u],u,c,o)}}else r=[];return r}function Jn(e,t,n={},s,r){if(de.ce||de.parent&&Ht(de.parent)&&de.parent.ce)return t!=="default"&&(n.name=t),X(),an(ve,null,[ne("slot",n,s)],64);let o=e[t];o&&o._c&&(o._d=!1),X();const i=o&&So(o(n)),l=n.key||i&&i.key,c=an(ve,{key:(l&&!Oe(l)?l:`_${t}`)+""},i||[],i&&e._===1?64:-2);return o&&o._c&&(o._d=!0),c}function So(e){return e.some(t=>fn(t)?!(t.type===et||t.type===ve&&!So(t.children)):!0)?e:null}const hs=e=>e?Ko(e)?Kn(e):hs(e.parent):null,tn=pe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>hs(e.parent),$root:e=>hs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Ao(e),$forceUpdate:e=>e.f||(e.f=()=>{Is(e.update)}),$nextTick:e=>e.n||(e.n=vo.bind(e.proxy)),$watch:e=>$l.bind(e)}),Xn=(e,t)=>e!==J&&!e.__isScriptSetup&&q(e,t),dl={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:l,appContext:c}=e;let h;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(Xn(s,t))return i[t]=1,s[t];if(r!==J&&q(r,t))return i[t]=2,r[t];if((h=e.propsOptions[0])&&q(h,t))return i[t]=3,o[t];if(n!==J&&q(n,t))return i[t]=4,n[t];ps&&(i[t]=0)}}const u=tn[t];let a,p;if(u)return t==="$attrs"&&fe(e.attrs,"get",""),u(e);if((a=l.__cssModules)&&(a=a[t]))return a;if(n!==J&&q(n,t))return i[t]=4,n[t];if(p=c.config.globalProperties,q(p,t))return p[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return Xn(r,t)?(r[t]=n,!0):s!==J&&q(s,t)?(s[t]=n,!0):q(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let l;return!!n[i]||e!==J&&q(e,i)||Xn(t,i)||(l=o[0])&&q(l,i)||q(s,i)||q(tn,i)||q(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:q(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Zs(e){return V(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let ps=!0;function hl(e){const t=Ao(e),n=e.proxy,s=e.ctx;ps=!1,t.beforeCreate&&Js(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:c,inject:h,created:u,beforeMount:a,mounted:p,beforeUpdate:m,updated:R,activated:P,deactivated:j,beforeDestroy:k,beforeUnmount:I,destroyed:z,unmounted:T,render:Q,renderTracked:ce,renderTriggered:re,errorCaptured:$e,serverPrefetch:st,expose:Ie,inheritAttrs:rt,components:pt,directives:He,filters:Ft}=t;if(h&&pl(h,s,null),i)for(const Y in i){const U=i[Y];N(U)&&(s[Y]=U.bind(n))}if(r){const Y=r.call(n,n);se(Y)&&(e.data=Dn(Y))}if(ps=!0,o)for(const Y in o){const U=o[Y],Ke=N(U)?U.bind(n,n):N(U.get)?U.get.bind(n,n):Fe,ot=!N(U)&&N(U.set)?U.set.bind(n):Fe,Le=Ce({get:Ke,set:ot});Object.defineProperty(s,Y,{enumerable:!0,configurable:!0,get:()=>Le.value,set:be=>Le.value=be})}if(l)for(const Y in l)Ro(l[Y],s,n,Y);if(c){const Y=N(c)?c.call(n):c;Reflect.ownKeys(Y).forEach(U=>{Cn(U,Y[U])})}u&&Js(u,e,"c");function le(Y,U){V(U)?U.forEach(Ke=>Y(Ke.bind(n))):U&&Y(U.bind(n))}if(le(sl,a),le(Ls,p),le(rl,m),le(ol,R),le(el,P),le(tl,j),le(al,$e),le(ul,ce),le(cl,re),le(il,I),le(ks,T),le(ll,st),V(Ie))if(Ie.length){const Y=e.exposed||(e.exposed={});Ie.forEach(U=>{Object.defineProperty(Y,U,{get:()=>n[U],set:Ke=>n[U]=Ke})})}else e.exposed||(e.exposed={});Q&&e.render===Fe&&(e.render=Q),rt!=null&&(e.inheritAttrs=rt),pt&&(e.components=pt),He&&(e.directives=He),st&&xo(e)}function pl(e,t,n=Fe){V(e)&&(e=gs(e));for(const s in e){const r=e[s];let o;se(r)?"default"in r?o=Ze(r.from||s,r.default,!0):o=Ze(r.from||s):o=Ze(r),he(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function Js(e,t,n){Ue(V(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Ro(e,t,n,s){let r=s.includes(".")?Do(n,s):()=>n[s];if(ie(e)){const o=t[e];N(o)&&kt(r,o)}else if(N(e))kt(r,e.bind(n));else if(se(e))if(V(e))e.forEach(o=>Ro(o,t,n,s));else{const o=N(e.handler)?e.handler.bind(n):t[e.handler];N(o)&&kt(r,o,e)}}function Ao(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let c;return l?c=l:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(h=>On(c,h,i,!0)),On(c,t,i)),se(t)&&o.set(t,c),c}function On(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&On(e,o,n,!0),r&&r.forEach(i=>On(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=gl[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const gl={data:Xs,props:er,emits:er,methods:Gt,computed:Gt,beforeCreate:ge,created:ge,beforeMount:ge,mounted:ge,beforeUpdate:ge,updated:ge,beforeDestroy:ge,beforeUnmount:ge,destroyed:ge,unmounted:ge,activated:ge,deactivated:ge,errorCaptured:ge,serverPrefetch:ge,components:Gt,directives:Gt,watch:vl,provide:Xs,inject:ml};function Xs(e,t){return t?e?function(){return pe(N(e)?e.call(this,this):e,N(t)?t.call(this,this):t)}:t:e}function ml(e,t){return Gt(gs(e),gs(t))}function gs(e){if(V(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ge(e,t){return e?[...new Set([].concat(e,t))]:t}function Gt(e,t){return e?pe(Object.create(null),e,t):t}function er(e,t){return e?V(e)&&V(t)?[...new Set([...e,...t])]:pe(Object.create(null),Zs(e),Zs(t??{})):t}function vl(e,t){if(!e)return t;if(!t)return e;const n=pe(Object.create(null),e);for(const s in t)n[s]=ge(e[s],t[s]);return n}function Mo(){return{app:null,config:{isNativeTag:ai,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let yl=0;function bl(e,t){return function(s,r=null){N(s)||(s=pe({},s)),r!=null&&!se(r)&&(r=null);const o=Mo(),i=new WeakSet,l=[];let c=!1;const h=o.app={_uid:yl++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:Jl,get config(){return o.config},set config(u){},use(u,...a){return i.has(u)||(u&&N(u.install)?(i.add(u),u.install(h,...a)):N(u)&&(i.add(u),u(h,...a))),h},mixin(u){return o.mixins.includes(u)||o.mixins.push(u),h},component(u,a){return a?(o.components[u]=a,h):o.components[u]},directive(u,a){return a?(o.directives[u]=a,h):o.directives[u]},mount(u,a,p){if(!c){const m=h._ceVNode||ne(s,r);return m.appContext=o,p===!0?p="svg":p===!1&&(p=void 0),e(m,u,p),c=!0,h._container=u,u.__vue_app__=h,Kn(m.component)}},onUnmount(u){l.push(u)},unmount(){c&&(Ue(l,h._instance,16),e(null,h._container),delete h._container.__vue_app__)},provide(u,a){return o.provides[u]=a,h},runWithContext(u){const a=Lt;Lt=h;try{return u()}finally{Lt=a}}};return h}}let Lt=null;function Cn(e,t){if(ye){let n=ye.provides;const s=ye.parent&&ye.parent.provides;s===n&&(n=ye.provides=Object.create(s)),n[e]=t}}function Ze(e,t,n=!1){const s=ye||de;if(s||Lt){let r=Lt?Lt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&N(t)?t.call(s&&s.proxy):t}}const Po={},Oo=()=>Object.create(Po),To=e=>Object.getPrototypeOf(e)===Po;function _l(e,t,n,s=!1){const r={},o=Oo();e.propsDefaults=Object.create(null),$o(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:ao(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function wl(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=W(r),[c]=e.propsOptions;let h=!1;if((s||i>0)&&!(i&16)){if(i&8){const u=e.vnode.dynamicProps;for(let a=0;a<u.length;a++){let p=u[a];if(Bn(e.emitsOptions,p))continue;const m=t[p];if(c)if(q(o,p))m!==o[p]&&(o[p]=m,h=!0);else{const R=ft(p);r[R]=ms(c,l,R,m,e,!1)}else m!==o[p]&&(o[p]=m,h=!0)}}}else{$o(e,t,r,o)&&(h=!0);let u;for(const a in l)(!t||!q(t,a)&&((u=wt(a))===a||!q(t,u)))&&(c?n&&(n[a]!==void 0||n[u]!==void 0)&&(r[a]=ms(c,l,a,void 0,e,!0)):delete r[a]);if(o!==l)for(const a in o)(!t||!q(t,a))&&(delete o[a],h=!0)}h&&Ye(e.attrs,"set","")}function $o(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let c in t){if(Zt(c))continue;const h=t[c];let u;r&&q(r,u=ft(c))?!o||!o.includes(u)?n[u]=h:(l||(l={}))[u]=h:Bn(e.emitsOptions,c)||(!(c in s)||h!==s[c])&&(s[c]=h,i=!0)}if(o){const c=W(n),h=l||J;for(let u=0;u<o.length;u++){const a=o[u];n[a]=ms(r,c,a,h[a],e,!q(h,a))}}return i}function ms(e,t,n,s,r,o){const i=e[n];if(i!=null){const l=q(i,"default");if(l&&s===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&N(c)){const{propsDefaults:h}=r;if(n in h)s=h[n];else{const u=vn(r);s=h[n]=c.call(null,t),u()}}else s=c;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!l?s=!1:i[1]&&(s===""||s===wt(n))&&(s=!0))}return s}const xl=new WeakMap;function Io(e,t,n=!1){const s=n?xl:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},l=[];let c=!1;if(!N(e)){const u=a=>{c=!0;const[p,m]=Io(a,t,!0);pe(i,p),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!o&&!c)return se(e)&&s.set(e,Pt),Pt;if(V(o))for(let u=0;u<o.length;u++){const a=ft(o[u]);tr(a)&&(i[a]=J)}else if(o)for(const u in o){const a=ft(u);if(tr(a)){const p=o[u],m=i[a]=V(p)||N(p)?{type:p}:pe({},p),R=m.type;let P=!1,j=!0;if(V(R))for(let k=0;k<R.length;++k){const I=R[k],z=N(I)&&I.name;if(z==="Boolean"){P=!0;break}else z==="String"&&(j=!1)}else P=N(R)&&R.name==="Boolean";m[0]=P,m[1]=j,(P||q(m,"default"))&&l.push(a)}}const h=[i,l];return se(e)&&s.set(e,h),h}function tr(e){return e[0]!=="$"&&!Zt(e)}const zs=e=>e[0]==="_"||e==="$stable",Vs=e=>V(e)?e.map(Ne):[Ne(e)],Cl=(e,t,n)=>{if(t._n)return t;const s=ae((...r)=>Vs(t(...r)),n);return s._c=!1,s},Ho=(e,t,n)=>{const s=e._ctx;for(const r in e){if(zs(r))continue;const o=e[r];if(N(o))t[r]=Cl(r,o,s);else if(o!=null){const i=Vs(o);t[r]=()=>i}}},Lo=(e,t)=>{const n=Vs(t);e.slots.default=()=>n},ko=(e,t,n)=>{for(const s in t)(n||!zs(s))&&(e[s]=t[s])},El=(e,t,n)=>{const s=e.slots=Oo();if(e.vnode.shapeFlag&32){const r=t.__;r&&ls(s,"__",r,!0);const o=t._;o?(ko(s,t,n),n&&ls(s,"_",o,!0)):Ho(t,s)}else t&&Lo(e,t)},Sl=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=J;if(s.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:ko(r,t,n):(o=!t.$stable,Ho(t,r)),i=t}else t&&(Lo(e,t),i={default:1});if(o)for(const l in r)!zs(l)&&i[l]==null&&delete r[l]},Se=jl;function Rl(e){return Al(e)}function Al(e,t){const n=kn();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:l,createComment:c,setText:h,setElementText:u,parentNode:a,nextSibling:p,setScopeId:m=Fe,insertStaticContent:R}=e,P=(f,d,g,v=null,_=null,b=null,S=void 0,E=null,C=!!d.dynamicChildren)=>{if(f===d)return;f&&!Kt(f,d)&&(v=y(f),be(f,_,b,!0),f=null),d.patchFlag===-2&&(C=!1,d.dynamicChildren=null);const{type:x,ref:L,shapeFlag:M}=d;switch(x){case Un:j(f,d,g,v);break;case et:k(f,d,g,v);break;case ts:f==null&&I(d,g,v,S);break;case ve:pt(f,d,g,v,_,b,S,E,C);break;default:M&1?Q(f,d,g,v,_,b,S,E,C):M&6?He(f,d,g,v,_,b,S,E,C):(M&64||M&128)&&x.process(f,d,g,v,_,b,S,E,C,$)}L!=null&&_?en(L,f&&f.ref,b,d||f,!d):L==null&&f&&f.ref!=null&&en(f.ref,null,b,f,!0)},j=(f,d,g,v)=>{if(f==null)s(d.el=l(d.children),g,v);else{const _=d.el=f.el;d.children!==f.children&&h(_,d.children)}},k=(f,d,g,v)=>{f==null?s(d.el=c(d.children||""),g,v):d.el=f.el},I=(f,d,g,v)=>{[f.el,f.anchor]=R(f.children,d,g,v,f.el,f.anchor)},z=({el:f,anchor:d},g,v)=>{let _;for(;f&&f!==d;)_=p(f),s(f,g,v),f=_;s(d,g,v)},T=({el:f,anchor:d})=>{let g;for(;f&&f!==d;)g=p(f),r(f),f=g;r(d)},Q=(f,d,g,v,_,b,S,E,C)=>{d.type==="svg"?S="svg":d.type==="math"&&(S="mathml"),f==null?ce(d,g,v,_,b,S,E,C):st(f,d,_,b,S,E,C)},ce=(f,d,g,v,_,b,S,E)=>{let C,x;const{props:L,shapeFlag:M,transition:H,dirs:D}=f;if(C=f.el=i(f.type,b,L&&L.is,L),M&8?u(C,f.children):M&16&&$e(f.children,C,null,v,_,es(f,b),S,E),D&&gt(f,null,v,"created"),re(C,f,f.scopeId,S,v),L){for(const ee in L)ee!=="value"&&!Zt(ee)&&o(C,ee,null,L[ee],b,v);"value"in L&&o(C,"value",null,L.value,b),(x=L.onVnodeBeforeMount)&&je(x,v,f)}D&&gt(f,null,v,"beforeMount");const B=Ml(_,H);B&&H.beforeEnter(C),s(C,d,g),((x=L&&L.onVnodeMounted)||B||D)&&Se(()=>{x&&je(x,v,f),B&&H.enter(C),D&&gt(f,null,v,"mounted")},_)},re=(f,d,g,v,_)=>{if(g&&m(f,g),v)for(let b=0;b<v.length;b++)m(f,v[b]);if(_){let b=_.subTree;if(d===b||Fo(b.type)&&(b.ssContent===d||b.ssFallback===d)){const S=_.vnode;re(f,S,S.scopeId,S.slotScopeIds,_.parent)}}},$e=(f,d,g,v,_,b,S,E,C=0)=>{for(let x=C;x<f.length;x++){const L=f[x]=E?ct(f[x]):Ne(f[x]);P(null,L,d,g,v,_,b,S,E)}},st=(f,d,g,v,_,b,S)=>{const E=d.el=f.el;let{patchFlag:C,dynamicChildren:x,dirs:L}=d;C|=f.patchFlag&16;const M=f.props||J,H=d.props||J;let D;if(g&&mt(g,!1),(D=H.onVnodeBeforeUpdate)&&je(D,g,d,f),L&&gt(d,f,g,"beforeUpdate"),g&&mt(g,!0),(M.innerHTML&&H.innerHTML==null||M.textContent&&H.textContent==null)&&u(E,""),x?Ie(f.dynamicChildren,x,E,g,v,es(d,_),b):S||U(f,d,E,null,g,v,es(d,_),b,!1),C>0){if(C&16)rt(E,M,H,g,_);else if(C&2&&M.class!==H.class&&o(E,"class",null,H.class,_),C&4&&o(E,"style",M.style,H.style,_),C&8){const B=d.dynamicProps;for(let ee=0;ee<B.length;ee++){const G=B[ee],_e=M[G],we=H[G];(we!==_e||G==="value")&&o(E,G,_e,we,_,g)}}C&1&&f.children!==d.children&&u(E,d.children)}else!S&&x==null&&rt(E,M,H,g,_);((D=H.onVnodeUpdated)||L)&&Se(()=>{D&&je(D,g,d,f),L&&gt(d,f,g,"updated")},v)},Ie=(f,d,g,v,_,b,S)=>{for(let E=0;E<d.length;E++){const C=f[E],x=d[E],L=C.el&&(C.type===ve||!Kt(C,x)||C.shapeFlag&198)?a(C.el):g;P(C,x,L,null,v,_,b,S,!0)}},rt=(f,d,g,v,_)=>{if(d!==g){if(d!==J)for(const b in d)!Zt(b)&&!(b in g)&&o(f,b,d[b],null,_,v);for(const b in g){if(Zt(b))continue;const S=g[b],E=d[b];S!==E&&b!=="value"&&o(f,b,E,S,_,v)}"value"in g&&o(f,"value",d.value,g.value,_)}},pt=(f,d,g,v,_,b,S,E,C)=>{const x=d.el=f?f.el:l(""),L=d.anchor=f?f.anchor:l("");let{patchFlag:M,dynamicChildren:H,slotScopeIds:D}=d;D&&(E=E?E.concat(D):D),f==null?(s(x,g,v),s(L,g,v),$e(d.children||[],g,L,_,b,S,E,C)):M>0&&M&64&&H&&f.dynamicChildren?(Ie(f.dynamicChildren,H,g,_,b,S,E),(d.key!=null||_&&d===_.subTree)&&zo(f,d,!0)):U(f,d,g,L,_,b,S,E,C)},He=(f,d,g,v,_,b,S,E,C)=>{d.slotScopeIds=E,f==null?d.shapeFlag&512?_.ctx.activate(d,g,v,S,C):Ft(d,g,v,_,b,S,C):xt(f,d,C)},Ft=(f,d,g,v,_,b,S)=>{const E=f.component=Wl(f,v,_);if(Co(f)&&(E.ctx.renderer=$),ql(E,!1,S),E.asyncDep){if(_&&_.registerDep(E,le,S),!f.el){const C=E.subTree=ne(et);k(null,C,d,g)}}else le(E,f,d,g,_,b,S)},xt=(f,d,g)=>{const v=d.component=f.component;if(zl(f,d,g))if(v.asyncDep&&!v.asyncResolved){Y(v,d,g);return}else v.next=d,v.update();else d.el=f.el,v.vnode=d},le=(f,d,g,v,_,b,S)=>{const E=()=>{if(f.isMounted){let{next:M,bu:H,u:D,parent:B,vnode:ee}=f;{const ze=Vo(f);if(ze){M&&(M.el=ee.el,Y(f,M,S)),ze.asyncDep.then(()=>{f.isUnmounted||E()});return}}let G=M,_e;mt(f,!1),M?(M.el=ee.el,Y(f,M,S)):M=ee,H&&xn(H),(_e=M.props&&M.props.onVnodeBeforeUpdate)&&je(_e,B,M,ee),mt(f,!0);const we=sr(f),ke=f.subTree;f.subTree=we,P(ke,we,a(ke.el),y(ke),f,_,b),M.el=we.el,G===null&&Vl(f,we.el),D&&Se(D,_),(_e=M.props&&M.props.onVnodeUpdated)&&Se(()=>je(_e,B,M,ee),_)}else{let M;const{el:H,props:D}=d,{bm:B,m:ee,parent:G,root:_e,type:we}=f,ke=Ht(d);mt(f,!1),B&&xn(B),!ke&&(M=D&&D.onVnodeBeforeMount)&&je(M,G,d),mt(f,!0);{_e.ce&&_e.ce._def.shadowRoot!==!1&&_e.ce._injectChildStyle(we);const ze=f.subTree=sr(f);P(null,ze,g,v,f,_,b),d.el=ze.el}if(ee&&Se(ee,_),!ke&&(M=D&&D.onVnodeMounted)){const ze=d;Se(()=>je(M,G,ze),_)}(d.shapeFlag&256||G&&Ht(G.vnode)&&G.vnode.shapeFlag&256)&&f.a&&Se(f.a,_),f.isMounted=!0,d=g=v=null}};f.scope.on();const C=f.effect=new Qr(E);f.scope.off();const x=f.update=C.run.bind(C),L=f.job=C.runIfDirty.bind(C);L.i=f,L.id=f.uid,C.scheduler=()=>Is(L),mt(f,!0),x()},Y=(f,d,g)=>{d.component=f;const v=f.vnode.props;f.vnode=d,f.next=null,wl(f,d.props,v,g),Sl(f,d.children,g),Je(),Ys(f),Xe()},U=(f,d,g,v,_,b,S,E,C=!1)=>{const x=f&&f.children,L=f?f.shapeFlag:0,M=d.children,{patchFlag:H,shapeFlag:D}=d;if(H>0){if(H&128){ot(x,M,g,v,_,b,S,E,C);return}else if(H&256){Ke(x,M,g,v,_,b,S,E,C);return}}D&8?(L&16&&Ae(x,_,b),M!==x&&u(g,M)):L&16?D&16?ot(x,M,g,v,_,b,S,E,C):Ae(x,_,b,!0):(L&8&&u(g,""),D&16&&$e(M,g,v,_,b,S,E,C))},Ke=(f,d,g,v,_,b,S,E,C)=>{f=f||Pt,d=d||Pt;const x=f.length,L=d.length,M=Math.min(x,L);let H;for(H=0;H<M;H++){const D=d[H]=C?ct(d[H]):Ne(d[H]);P(f[H],D,g,null,_,b,S,E,C)}x>L?Ae(f,_,b,!0,!1,M):$e(d,g,v,_,b,S,E,C,M)},ot=(f,d,g,v,_,b,S,E,C)=>{let x=0;const L=d.length;let M=f.length-1,H=L-1;for(;x<=M&&x<=H;){const D=f[x],B=d[x]=C?ct(d[x]):Ne(d[x]);if(Kt(D,B))P(D,B,g,null,_,b,S,E,C);else break;x++}for(;x<=M&&x<=H;){const D=f[M],B=d[H]=C?ct(d[H]):Ne(d[H]);if(Kt(D,B))P(D,B,g,null,_,b,S,E,C);else break;M--,H--}if(x>M){if(x<=H){const D=H+1,B=D<L?d[D].el:v;for(;x<=H;)P(null,d[x]=C?ct(d[x]):Ne(d[x]),g,B,_,b,S,E,C),x++}}else if(x>H)for(;x<=M;)be(f[x],_,b,!0),x++;else{const D=x,B=x,ee=new Map;for(x=B;x<=H;x++){const Ee=d[x]=C?ct(d[x]):Ne(d[x]);Ee.key!=null&&ee.set(Ee.key,x)}let G,_e=0;const we=H-B+1;let ke=!1,ze=0;const Bt=new Array(we);for(x=0;x<we;x++)Bt[x]=0;for(x=D;x<=M;x++){const Ee=f[x];if(_e>=we){be(Ee,_,b,!0);continue}let Ve;if(Ee.key!=null)Ve=ee.get(Ee.key);else for(G=B;G<=H;G++)if(Bt[G-B]===0&&Kt(Ee,d[G])){Ve=G;break}Ve===void 0?be(Ee,_,b,!0):(Bt[Ve-B]=x+1,Ve>=ze?ze=Ve:ke=!0,P(Ee,d[Ve],g,null,_,b,S,E,C),_e++)}const Bs=ke?Pl(Bt):Pt;for(G=Bs.length-1,x=we-1;x>=0;x--){const Ee=B+x,Ve=d[Ee],Us=Ee+1<L?d[Ee+1].el:v;Bt[x]===0?P(null,Ve,g,Us,_,b,S,E,C):ke&&(G<0||x!==Bs[G]?Le(Ve,g,Us,2):G--)}}},Le=(f,d,g,v,_=null)=>{const{el:b,type:S,transition:E,children:C,shapeFlag:x}=f;if(x&6){Le(f.component.subTree,d,g,v);return}if(x&128){f.suspense.move(d,g,v);return}if(x&64){S.move(f,d,g,$);return}if(S===ve){s(b,d,g);for(let M=0;M<C.length;M++)Le(C[M],d,g,v);s(f.anchor,d,g);return}if(S===ts){z(f,d,g);return}if(v!==2&&x&1&&E)if(v===0)E.beforeEnter(b),s(b,d,g),Se(()=>E.enter(b),_);else{const{leave:M,delayLeave:H,afterLeave:D}=E,B=()=>{f.ctx.isUnmounted?r(b):s(b,d,g)},ee=()=>{M(b,()=>{B(),D&&D()})};H?H(b,B,ee):ee()}else s(b,d,g)},be=(f,d,g,v=!1,_=!1)=>{const{type:b,props:S,ref:E,children:C,dynamicChildren:x,shapeFlag:L,patchFlag:M,dirs:H,cacheIndex:D}=f;if(M===-2&&(_=!1),E!=null&&(Je(),en(E,null,g,f,!0),Xe()),D!=null&&(d.renderCache[D]=void 0),L&256){d.ctx.deactivate(f);return}const B=L&1&&H,ee=!Ht(f);let G;if(ee&&(G=S&&S.onVnodeBeforeUnmount)&&je(G,d,f),L&6)yn(f.component,g,v);else{if(L&128){f.suspense.unmount(g,v);return}B&&gt(f,null,d,"beforeUnmount"),L&64?f.type.remove(f,d,g,$,v):x&&!x.hasOnce&&(b!==ve||M>0&&M&64)?Ae(x,d,g,!1,!0):(b===ve&&M&384||!_&&L&16)&&Ae(C,d,g),v&&Ct(f)}(ee&&(G=S&&S.onVnodeUnmounted)||B)&&Se(()=>{G&&je(G,d,f),B&&gt(f,null,d,"unmounted")},g)},Ct=f=>{const{type:d,el:g,anchor:v,transition:_}=f;if(d===ve){Et(g,v);return}if(d===ts){T(f);return}const b=()=>{r(g),_&&!_.persisted&&_.afterLeave&&_.afterLeave()};if(f.shapeFlag&1&&_&&!_.persisted){const{leave:S,delayLeave:E}=_,C=()=>S(g,b);E?E(f.el,b,C):C()}else b()},Et=(f,d)=>{let g;for(;f!==d;)g=p(f),r(f),f=g;r(d)},yn=(f,d,g)=>{const{bum:v,scope:_,job:b,subTree:S,um:E,m:C,a:x,parent:L,slots:{__:M}}=f;nr(C),nr(x),v&&xn(v),L&&V(M)&&M.forEach(H=>{L.renderCache[H]=void 0}),_.stop(),b&&(b.flags|=8,be(S,f,d,g)),E&&Se(E,d),Se(()=>{f.isUnmounted=!0},d),d&&d.pendingBranch&&!d.isUnmounted&&f.asyncDep&&!f.asyncResolved&&f.suspenseId===d.pendingId&&(d.deps--,d.deps===0&&d.resolve())},Ae=(f,d,g,v=!1,_=!1,b=0)=>{for(let S=b;S<f.length;S++)be(f[S],d,g,v,_)},y=f=>{if(f.shapeFlag&6)return y(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const d=p(f.anchor||f.el),g=d&&d[Ji];return g?p(g):d};let O=!1;const A=(f,d,g)=>{f==null?d._vnode&&be(d._vnode,null,null,!0):P(d._vnode||null,f,d,null,null,null,g),d._vnode=f,O||(O=!0,Ys(),bo(),O=!1)},$={p:P,um:be,m:Le,r:Ct,mt:Ft,mc:$e,pc:U,pbc:Ie,n:y,o:e};return{render:A,hydrate:void 0,createApp:bl(A)}}function es({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function mt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Ml(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function zo(e,t,n=!1){const s=e.children,r=t.children;if(V(s)&&V(r))for(let o=0;o<s.length;o++){const i=s[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=ct(r[o]),l.el=i.el),!n&&l.patchFlag!==-2&&zo(i,l)),l.type===Un&&(l.el=i.el),l.type===et&&!l.el&&(l.el=i.el)}}function Pl(e){const t=e.slice(),n=[0];let s,r,o,i,l;const c=e.length;for(s=0;s<c;s++){const h=e[s];if(h!==0){if(r=n[n.length-1],e[r]<h){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<h?o=l+1:i=l;h<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Vo(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Vo(t)}function nr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Ol=Symbol.for("v-scx"),Tl=()=>Ze(Ol);function kt(e,t,n){return jo(e,t,n)}function jo(e,t,n=J){const{immediate:s,deep:r,flush:o,once:i}=n,l=pe({},n),c=t&&s||!t&&o!=="post";let h;if(dn){if(o==="sync"){const m=Tl();h=m.__watcherHandles||(m.__watcherHandles=[])}else if(!c){const m=()=>{};return m.stop=Fe,m.resume=Fe,m.pause=Fe,m}}const u=ye;l.call=(m,R,P)=>Ue(m,u,R,P);let a=!1;o==="post"?l.scheduler=m=>{Se(m,u&&u.suspense)}:o!=="sync"&&(a=!0,l.scheduler=(m,R)=>{R?m():Is(m)}),l.augmentJob=m=>{t&&(m.flags|=4),a&&(m.flags|=2,u&&(m.id=u.uid,m.i=u))};const p=Gi(e,t,l);return dn&&(h?h.push(p):c&&p()),p}function $l(e,t,n){const s=this.proxy,r=ie(e)?e.includes(".")?Do(s,e):()=>s[e]:e.bind(s,s);let o;N(t)?o=t:(o=t.handler,n=t);const i=vn(this),l=jo(r,o.bind(s),n);return i(),l}function Do(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const Il=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${ft(t)}Modifiers`]||e[`${wt(t)}Modifiers`];function Hl(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||J;let r=n;const o=t.startsWith("update:"),i=o&&Il(s,t.slice(7));i&&(i.trim&&(r=n.map(u=>ie(u)?u.trim():u)),i.number&&(r=n.map(cs)));let l,c=s[l=qn(t)]||s[l=qn(ft(t))];!c&&o&&(c=s[l=qn(wt(t))]),c&&Ue(c,e,6,r);const h=s[l+"Once"];if(h){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Ue(h,e,6,r)}}function No(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!N(e)){const c=h=>{const u=No(h,t,!0);u&&(l=!0,pe(i,u))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!o&&!l?(se(e)&&s.set(e,null),null):(V(o)?o.forEach(c=>i[c]=null):pe(i,o),se(e)&&s.set(e,i),i)}function Bn(e,t){return!e||!In(t)?!1:(t=t.slice(2).replace(/Once$/,""),q(e,t[0].toLowerCase()+t.slice(1))||q(e,wt(t))||q(e,t))}function sr(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:c,render:h,renderCache:u,props:a,data:p,setupState:m,ctx:R,inheritAttrs:P}=e,j=Pn(e);let k,I;try{if(n.shapeFlag&4){const T=r||s,Q=T;k=Ne(h.call(Q,T,u,a,m,p,R)),I=l}else{const T=t;k=Ne(T.length>1?T(a,{attrs:l,slots:i,emit:c}):T(a,null)),I=t.props?l:Ll(l)}}catch(T){nn.length=0,Nn(T,e,1),k=ne(et)}let z=k;if(I&&P!==!1){const T=Object.keys(I),{shapeFlag:Q}=z;T.length&&Q&7&&(o&&T.some(Cs)&&(I=kl(I,o)),z=jt(z,I,!1,!0))}return n.dirs&&(z=jt(z,null,!1,!0),z.dirs=z.dirs?z.dirs.concat(n.dirs):n.dirs),n.transition&&Hs(z,n.transition),k=z,Pn(j),k}const Ll=e=>{let t;for(const n in e)(n==="class"||n==="style"||In(n))&&((t||(t={}))[n]=e[n]);return t},kl=(e,t)=>{const n={};for(const s in e)(!Cs(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function zl(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:l,patchFlag:c}=t,h=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?rr(s,i,h):!!i;if(c&8){const u=t.dynamicProps;for(let a=0;a<u.length;a++){const p=u[a];if(i[p]!==s[p]&&!Bn(h,p))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?rr(s,i,h):!0:!!i;return!1}function rr(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!Bn(n,o))return!0}return!1}function Vl({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Fo=e=>e.__isSuspense;function jl(e,t){t&&t.pendingBranch?V(e)?t.effects.push(...e):t.effects.push(e):Zi(e)}const ve=Symbol.for("v-fgt"),Un=Symbol.for("v-txt"),et=Symbol.for("v-cmt"),ts=Symbol.for("v-stc"),nn=[];let Re=null;function X(e=!1){nn.push(Re=e?null:[])}function Dl(){nn.pop(),Re=nn[nn.length-1]||null}let un=1;function or(e,t=!1){un+=e,e<0&&Re&&t&&(Re.hasOnce=!0)}function Bo(e){return e.dynamicChildren=un>0?Re||Pt:null,Dl(),un>0&&Re&&Re.push(e),e}function oe(e,t,n,s,r,o){return Bo(w(e,t,n,s,r,o,!0))}function an(e,t,n,s,r){return Bo(ne(e,t,n,s,r,!0))}function fn(e){return e?e.__v_isVNode===!0:!1}function Kt(e,t){return e.type===t.type&&e.key===t.key}const Uo=({key:e})=>e??null,En=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ie(e)||he(e)||N(e)?{i:de,r:e,k:t,f:!!n}:e:null);function w(e,t=null,n=null,s=0,r=null,o=e===ve?0:1,i=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Uo(t),ref:t&&En(t),scopeId:wo,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:de};return l?(js(c,n),o&128&&e.normalize(c)):n&&(c.shapeFlag|=ie(n)?8:16),un>0&&!i&&Re&&(c.patchFlag>0||o&6)&&c.patchFlag!==32&&Re.push(c),c}const ne=Nl;function Nl(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===fl)&&(e=et),fn(e)){const l=jt(e,t,!0);return n&&js(l,n),un>0&&!o&&Re&&(l.shapeFlag&6?Re[Re.indexOf(e)]=l:Re.push(l)),l.patchFlag=-2,l}if(Zl(e)&&(e=e.__vccOpts),t){t=Fl(t);let{class:l,style:c}=t;l&&!ie(l)&&(t.class=zn(l)),se(c)&&($s(c)&&!V(c)&&(c=pe({},c)),t.style=bt(c))}const i=ie(e)?1:Fo(e)?128:Xi(e)?64:se(e)?4:N(e)?2:0;return w(e,t,n,s,r,i,o,!0)}function Fl(e){return e?$s(e)||To(e)?pe({},e):e:null}function jt(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:c}=e,h=t?Bl(r||{},t):r,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:h,key:h&&Uo(h),ref:t&&t.ref?n&&o?V(o)?o.concat(En(t)):[o,En(t)]:En(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ve?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&jt(e.ssContent),ssFallback:e.ssFallback&&jt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&Hs(u,c.clone(u)),u}function F(e=" ",t=0){return ne(Un,null,e,t)}function Yt(e="",t=!1){return t?(X(),an(et,null,e)):ne(et,null,e)}function Ne(e){return e==null||typeof e=="boolean"?ne(et):V(e)?ne(ve,null,e.slice()):fn(e)?ct(e):ne(Un,null,String(e))}function ct(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:jt(e)}function js(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(V(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),js(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!To(t)?t._ctx=de:r===3&&de&&(de.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else N(t)?(t={default:t,_ctx:de},n=32):(t=String(t),s&64?(n=16,t=[F(t)]):n=8);e.children=t,e.shapeFlag|=n}function Bl(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=zn([t.class,s.class]));else if(r==="style")t.style=bt([t.style,s.style]);else if(In(r)){const o=t[r],i=s[r];i&&o!==i&&!(V(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function je(e,t,n,s=null){Ue(e,t,7,[n,s])}const Ul=Mo();let Kl=0;function Wl(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||Ul,o={uid:Kl++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Yr(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Io(s,r),emitsOptions:No(s,r),emit:null,emitted:null,propsDefaults:J,inheritAttrs:s.inheritAttrs,ctx:J,data:J,props:J,attrs:J,slots:J,refs:J,setupState:J,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=Hl.bind(null,o),e.ce&&e.ce(o),o}let ye=null,Tn,vs;{const e=kn(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};Tn=t("__VUE_INSTANCE_SETTERS__",n=>ye=n),vs=t("__VUE_SSR_SETTERS__",n=>dn=n)}const vn=e=>{const t=ye;return Tn(e),e.scope.on(),()=>{e.scope.off(),Tn(t)}},ir=()=>{ye&&ye.scope.off(),Tn(null)};function Ko(e){return e.vnode.shapeFlag&4}let dn=!1;function ql(e,t=!1,n=!1){t&&vs(t);const{props:s,children:r}=e.vnode,o=Ko(e);_l(e,s,o,t),El(e,r,n||t);const i=o?Gl(e,t):void 0;return t&&vs(!1),i}function Gl(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,dl);const{setup:s}=n;if(s){Je();const r=e.setupContext=s.length>1?Ql(e):null,o=vn(e),i=mn(s,e,0,[e.props,r]),l=Nr(i);if(Xe(),o(),(l||e.sp)&&!Ht(e)&&xo(e),l){if(i.then(ir,ir),t)return i.then(c=>{lr(e,c)}).catch(c=>{Nn(c,e,0)});e.asyncDep=i}else lr(e,i)}else Wo(e)}function lr(e,t,n){N(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:se(t)&&(e.setupState=go(t)),Wo(e)}function Wo(e,t,n){const s=e.type;e.render||(e.render=s.render||Fe);{const r=vn(e);Je();try{hl(e)}finally{Xe(),r()}}}const Yl={get(e,t){return fe(e,"get",""),e[t]}};function Ql(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Yl),slots:e.slots,emit:e.emit,expose:t}}function Kn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(go(ho(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in tn)return tn[n](e)},has(t,n){return n in t||n in tn}})):e.proxy}function Zl(e){return N(e)&&"__vccOpts"in e}const Ce=(e,t)=>Wi(e,t,dn);function qo(e,t,n){const s=arguments.length;return s===2?se(t)&&!V(t)?fn(t)?ne(e,null,[t]):ne(e,t):ne(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&fn(n)&&(n=[n]),ne(e,t,n))}const Jl="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ys;const cr=typeof window<"u"&&window.trustedTypes;if(cr)try{ys=cr.createPolicy("vue",{createHTML:e=>e})}catch{}const Go=ys?e=>ys.createHTML(e):e=>e,Xl="http://www.w3.org/2000/svg",ec="http://www.w3.org/1998/Math/MathML",Ge=typeof document<"u"?document:null,ur=Ge&&Ge.createElement("template"),tc={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?Ge.createElementNS(Xl,e):t==="mathml"?Ge.createElementNS(ec,e):n?Ge.createElement(e,{is:n}):Ge.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>Ge.createTextNode(e),createComment:e=>Ge.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ge.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{ur.innerHTML=Go(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=ur.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},nc=Symbol("_vtc");function sc(e,t,n){const s=e[nc];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const ar=Symbol("_vod"),rc=Symbol("_vsh"),oc=Symbol(""),ic=/(^|;)\s*display\s*:/;function lc(e,t,n){const s=e.style,r=ie(n);let o=!1;if(n&&!r){if(t)if(ie(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&Sn(s,l,"")}else for(const i in t)n[i]==null&&Sn(s,i,"");for(const i in n)i==="display"&&(o=!0),Sn(s,i,n[i])}else if(r){if(t!==n){const i=s[oc];i&&(n+=";"+i),s.cssText=n,o=ic.test(n)}}else t&&e.removeAttribute("style");ar in e&&(e[ar]=o?s.display:"",e[rc]&&(s.display="none"))}const fr=/\s*!important$/;function Sn(e,t,n){if(V(n))n.forEach(s=>Sn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=cc(e,t);fr.test(n)?e.setProperty(wt(s),n.replace(fr,""),"important"):e[s]=n}}const dr=["Webkit","Moz","ms"],ns={};function cc(e,t){const n=ns[t];if(n)return n;let s=ft(t);if(s!=="filter"&&s in e)return ns[t]=s;s=Ur(s);for(let r=0;r<dr.length;r++){const o=dr[r]+s;if(o in e)return ns[t]=o}return t}const hr="http://www.w3.org/1999/xlink";function pr(e,t,n,s,r,o=_i(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(hr,t.slice(6,t.length)):e.setAttributeNS(hr,t,n):n==null||o&&!Kr(n)?e.removeAttribute(t):e.setAttribute(t,o?"":Oe(n)?String(n):n)}function gr(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Go(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Kr(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function yt(e,t,n,s){e.addEventListener(t,n,s)}function uc(e,t,n,s){e.removeEventListener(t,n,s)}const mr=Symbol("_vei");function ac(e,t,n,s,r=null){const o=e[mr]||(e[mr]={}),i=o[t];if(s&&i)i.value=s;else{const[l,c]=fc(t);if(s){const h=o[t]=pc(s,r);yt(e,l,h,c)}else i&&(uc(e,l,i,c),o[t]=void 0)}}const vr=/(?:Once|Passive|Capture)$/;function fc(e){let t;if(vr.test(e)){t={};let s;for(;s=e.match(vr);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):wt(e.slice(2)),t]}let ss=0;const dc=Promise.resolve(),hc=()=>ss||(dc.then(()=>ss=0),ss=Date.now());function pc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;Ue(gc(s,n.value),t,5,[s])};return n.value=e,n.attached=hc(),n}function gc(e,t){if(V(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const yr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,mc=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?sc(e,s,i):t==="style"?lc(e,n,s):In(t)?Cs(t)||ac(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):vc(e,t,s,i))?(gr(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&pr(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ie(s))?gr(e,ft(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),pr(e,t,s,i))};function vc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&yr(t)&&N(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return yr(t)&&ie(n)?!1:t in e}const $n=e=>{const t=e.props["onUpdate:modelValue"]||!1;return V(t)?n=>xn(t,n):t};function yc(e){e.target.composing=!0}function br(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const zt=Symbol("_assign"),Qt={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[zt]=$n(r);const o=s||r.props&&r.props.type==="number";yt(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=cs(l)),e[zt](l)}),n&&yt(e,"change",()=>{e.value=e.value.trim()}),t||(yt(e,"compositionstart",yc),yt(e,"compositionend",br),yt(e,"change",br))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[zt]=$n(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?cs(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===c)||(e.value=c))}},bc={deep:!0,created(e,t,n){e[zt]=$n(n),yt(e,"change",()=>{const s=e._modelValue,r=_c(e),o=e.checked,i=e[zt];if(V(s)){const l=Wr(s,r),c=l!==-1;if(o&&!c)i(s.concat(r));else if(!o&&c){const h=[...s];h.splice(l,1),i(h)}}else if(Hn(s)){const l=new Set(s);o?l.add(r):l.delete(r),i(l)}else i(Yo(e,o))})},mounted:_r,beforeUpdate(e,t,n){e[zt]=$n(n),_r(e,t,n)}};function _r(e,{value:t,oldValue:n},s){e._modelValue=t;let r;if(V(t))r=Wr(t,s.props.value)>-1;else if(Hn(t))r=t.has(s.props.value);else{if(t===n)return;r=Vn(t,Yo(e,!0))}e.checked!==r&&(e.checked=r)}function _c(e){return"_value"in e?e._value:e.value}function Yo(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const wc=["ctrl","shift","alt","meta"],xc={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>wc.some(n=>e[`${n}Key`]&&!t.includes(n))},Qo=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...o)=>{for(let i=0;i<t.length;i++){const l=xc[t[i]];if(l&&l(r,t))return}return e(r,...o)})},Cc=pe({patchProp:mc},tc);let wr;function Ec(){return wr||(wr=Rl(Cc))}const Sc=(...e)=>{const t=Ec().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=Ac(s);if(!r)return;const o=t._component;!N(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,Rc(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function Rc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Ac(e){return ie(e)?document.querySelector(e):e}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const Mc=Symbol();var xr;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(xr||(xr={}));function Pc(){const e=xi(!0),t=e.run(()=>Be({}));let n=[],s=[];const r=ho({install(o){r._a=o,o.provide(Mc,r),o.config.globalProperties.$pinia=r,s.forEach(i=>n.push(i)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const Oc={class:"material-preview"},Tc={key:3,class:"alpha-indicator"},$c={class:"material-info"},Ic={class:"material-name"},Hc={class:"material-details"},Lc={key:0,class:"detail-tag texture-tag"},kc={key:1,class:"detail-tag alpha-tag"},zc={class:"detail-tag color-tag"},Vc={key:0,class:"dropdown-menu"},jc=ht({__name:"MaterialCard",props:{material:{}},emits:["apply","export","delete"],setup(e,{emit:t}){const n=e,s=t,r=Be(!1),o=Be(),i=Ce(()=>{const[u,a,p]=n.material.color;return`rgb(${Math.round(u*255)}, ${Math.round(a*255)}, ${Math.round(p*255)})`}),l=()=>{s("export"),r.value=!1},c=()=>{confirm(`确定要删除材质 "${n.material.display_name||n.material.name}" 吗？`)&&s("delete"),r.value=!1},h=u=>{o.value&&!o.value.contains(u.target)&&(r.value=!1)};return Ls(()=>{document.addEventListener("click",h)}),ks(()=>{document.removeEventListener("click",h)}),(u,a)=>(X(),oe("div",{class:"material-card",onClick:a[3]||(a[3]=p=>u.$emit("apply"))},[w("div",Oc,[u.material.icon_data&&u.material.icon_data.startsWith("file://")?(X(),oe("div",{key:0,class:"texture-preview",style:bt({backgroundImage:`url('${u.material.icon_data}')`})},null,4)):u.material.texture_path?(X(),oe("div",{key:1,class:"texture-preview",style:bt({backgroundImage:`url('file:///${u.material.texture_path.replace(/\\/g,"/")}')`})},null,4)):(X(),oe("div",{key:2,class:"color-preview",style:bt({backgroundColor:i.value})},null,4)),u.material.use_alpha?(X(),oe("div",Tc,a[4]||(a[4]=[w("svg",{viewBox:"0 0 24 24",width:"16",height:"16"},[w("path",{fill:"white",d:"M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z"})],-1)]))):Yt("",!0)]),w("div",$c,[w("h3",Ic,Vt(u.material.display_name||u.material.name),1),w("div",Hc,[u.material.texture?(X(),oe("span",Lc,"纹理")):Yt("",!0),u.material.use_alpha?(X(),oe("span",kc,"透明")):Yt("",!0),w("span",zc,Vt(i.value),1)])]),w("div",{class:"material-actions",onClick:a[2]||(a[2]=Qo(()=>{},["stop"]))},[w("button",{class:"action-btn",onClick:a[0]||(a[0]=p=>u.$emit("apply")),title:"应用材质"},a[5]||(a[5]=[w("svg",{viewBox:"0 0 24 24",width:"16",height:"16"},[w("path",{fill:"currentColor",d:"M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"})],-1)])),w("div",{class:"dropdown",ref_key:"dropdownRef",ref:o},[w("button",{class:"action-btn dropdown-toggle",onClick:a[1]||(a[1]=p=>r.value=!r.value),title:"更多操作"},a[6]||(a[6]=[w("svg",{viewBox:"0 0 24 24",width:"16",height:"16"},[w("path",{fill:"currentColor",d:"M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z"})],-1)])),r.value?(X(),oe("div",Vc,[w("button",{class:"dropdown-item",onClick:l},a[7]||(a[7]=[w("svg",{viewBox:"0 0 24 24",width:"16",height:"16"},[w("path",{fill:"currentColor",d:"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"})],-1),F(" 导出 ")])),w("button",{class:"dropdown-item delete",onClick:c},a[8]||(a[8]=[w("svg",{viewBox:"0 0 24 24",width:"16",height:"16"},[w("path",{fill:"currentColor",d:"M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"})],-1),F(" 删除 ")]))])):Yt("",!0)],512)])]))}}),nt=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},Dc=nt(jc,[["__scopeId","data-v-f37ca5b1"]]),Nc={class:"dialog-header"},Fc={class:"dialog-content"},Bc={class:"form-group"},Uc={class:"form-group"},Kc={class:"color-input-group"},Wc={class:"form-group"},qc={class:"slider-group"},Gc={class:"slider-value"},Yc={class:"form-group"},Qc={class:"preview-section"},Zc={class:"material-preview"},Jc={class:"dialog-footer"},Xc=["disabled"],eu=ht({__name:"CreateMaterialDialog",emits:["close","create"],setup(e,{emit:t}){const n=t,s=Be({name:"",color:[1,1,1],alpha:1,use_alpha:!1}),r=Be("#ffffff");kt(r,i=>{const l=i.replace("#",""),c=parseInt(l.substr(0,2),16)/255,h=parseInt(l.substr(2,2),16)/255,u=parseInt(l.substr(4,2),16)/255;s.value.color=[c,h,u]}),kt(()=>s.value.color,i=>{const[l,c,h]=i,u="#"+Math.round(l*255).toString(16).padStart(2,"0")+Math.round(c*255).toString(16).padStart(2,"0")+Math.round(h*255).toString(16).padStart(2,"0");r.value=u},{deep:!0});const o=()=>{if(!s.value.name.trim()){alert("请输入材质名称");return}n("create",{...s.value})};return(i,l)=>(X(),oe("div",{class:"dialog-overlay",onClick:l[8]||(l[8]=c=>i.$emit("close"))},[w("div",{class:"dialog",onClick:l[7]||(l[7]=Qo(()=>{},["stop"]))},[w("div",Nc,[l[10]||(l[10]=w("h2",null,"创建新材质",-1)),w("button",{class:"close-btn",onClick:l[0]||(l[0]=c=>i.$emit("close"))},l[9]||(l[9]=[w("svg",{viewBox:"0 0 24 24",width:"20",height:"20"},[w("path",{fill:"currentColor",d:"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"})],-1)]))]),w("div",Fc,[w("div",Bc,[l[11]||(l[11]=w("label",{for:"materialName"},"材质名称",-1)),At(w("input",{id:"materialName","onUpdate:modelValue":l[1]||(l[1]=c=>s.value.name=c),type:"text",class:"input",placeholder:"输入材质名称",required:""},null,512),[[Qt,s.value.name]])]),w("div",Uc,[l[12]||(l[12]=w("label",{for:"materialColor"},"颜色",-1)),w("div",Kc,[At(w("input",{id:"materialColor","onUpdate:modelValue":l[2]||(l[2]=c=>r.value=c),type:"color",class:"color-picker"},null,512),[[Qt,r.value]]),At(w("input",{"onUpdate:modelValue":l[3]||(l[3]=c=>r.value=c),type:"text",class:"input color-text",placeholder:"#ffffff"},null,512),[[Qt,r.value]])])]),w("div",Wc,[l[13]||(l[13]=w("label",{for:"materialAlpha"},"透明度",-1)),w("div",qc,[At(w("input",{id:"materialAlpha","onUpdate:modelValue":l[4]||(l[4]=c=>s.value.alpha=c),type:"range",min:"0",max:"1",step:"0.01",class:"slider"},null,512),[[Qt,s.value.alpha]]),w("span",Gc,Vt(Math.round(s.value.alpha*100))+"%",1)])]),w("div",Yc,[w("label",null,[At(w("input",{"onUpdate:modelValue":l[5]||(l[5]=c=>s.value.use_alpha=c),type:"checkbox",class:"checkbox"},null,512),[[bc,s.value.use_alpha]]),l[14]||(l[14]=F(" 使用透明度 "))])]),w("div",Qc,[l[15]||(l[15]=w("h3",null,"预览",-1)),w("div",Zc,[w("div",{class:"preview-swatch",style:bt({backgroundColor:r.value,opacity:s.value.use_alpha?s.value.alpha:1})},null,4)])])]),w("div",Jc,[w("button",{class:"btn btn-secondary",onClick:l[6]||(l[6]=c=>i.$emit("close"))},"取消"),w("button",{class:"btn btn-primary",onClick:o,disabled:!s.value.name}," 创建 ",8,Xc)])])]))}}),tu=nt(eu,[["__scopeId","data-v-0b5ddc05"]]),nu={class:"material-browser"},su={class:"toolbar"},ru={class:"toolbar-left"},ou={class:"search-container"},iu={class:"toolbar-right"},lu={class:"categories"},cu=["onClick"],uu={class:"category-count"},au={class:"materials-grid"},fu=ht({__name:"MaterialBrowser",props:{materials:{}},emits:["apply-material","create-material","delete-material","refresh-materials","export-material"],setup(e,{emit:t}){const n=e,s=t,r=Be(""),o=Be("all"),i=Be(!1),l=Ce(()=>[{id:"all",name:"全部",count:n.materials.length},{id:"textured",name:"纹理",count:n.materials.filter(u=>u.texture).length},{id:"solid",name:"纯色",count:n.materials.filter(u=>!u.texture).length},{id:"transparent",name:"透明",count:n.materials.filter(u=>u.use_alpha).length}]),c=Ce(()=>{let u=n.materials;if(o.value!=="all")switch(o.value){case"textured":u=u.filter(a=>a.texture);break;case"solid":u=u.filter(a=>!a.texture);break;case"transparent":u=u.filter(a=>a.use_alpha);break}if(r.value){const a=r.value.toLowerCase();u=u.filter(p=>p.name.toLowerCase().includes(a)||p.display_name.toLowerCase().includes(a))}return u}),h=u=>{s("create-material",u),i.value=!1};return(u,a)=>(X(),oe("div",nu,[w("div",su,[w("div",ru,[w("div",ou,[At(w("input",{"onUpdate:modelValue":a[0]||(a[0]=p=>r.value=p),type:"text",placeholder:"搜索材质...",class:"search-input"},null,512),[[Qt,r.value]]),a[4]||(a[4]=w("svg",{class:"search-icon",viewBox:"0 0 24 24",width:"16",height:"16"},[w("path",{fill:"currentColor",d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"})],-1))])]),w("div",iu,[w("button",{class:"btn btn-secondary",onClick:a[1]||(a[1]=p=>u.$emit("refresh-materials")),title:"刷新材质列表"},a[5]||(a[5]=[w("svg",{viewBox:"0 0 24 24",width:"16",height:"16"},[w("path",{fill:"currentColor",d:"M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"})],-1),F(" 刷新 ")])),w("button",{class:"btn btn-primary",onClick:a[2]||(a[2]=p=>i.value=!0)},a[6]||(a[6]=[w("svg",{viewBox:"0 0 24 24",width:"16",height:"16"},[w("path",{fill:"currentColor",d:"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"})],-1),F(" 新建 ")]))])]),w("div",lu,[(X(!0),oe(ve,null,Qs(l.value,p=>(X(),oe("button",{key:p.id,class:zn(["category-btn",{active:o.value===p.id}]),onClick:m=>o.value=p.id},[F(Vt(p.name)+" ",1),w("span",uu,Vt(p.count),1)],10,cu))),128))]),w("div",au,[(X(!0),oe(ve,null,Qs(c.value,p=>(X(),an(Dc,{key:p.name,material:p,onApply:m=>u.$emit("apply-material",p.name),onExport:m=>u.$emit("export-material",p.name),onDelete:m=>u.$emit("delete-material",p.name)},null,8,["material","onApply","onExport","onDelete"]))),128))]),i.value?(X(),an(tu,{key:0,onClose:a[3]||(a[3]=p=>i.value=!1),onCreate:h})):Yt("",!0)]))}}),du=nt(fu,[["__scopeId","data-v-3ed12ee4"]]),hu={id:"app"},pu=ht({__name:"App",setup(e){const t=Be([]);window.receiveMaterials=r=>{t.value=r};const n=(r,o)=>{window.sketchup?window.sketchup.callback(r,o):console.log("Ruby callback:",r,o)},s=()=>{n("refreshMaterials")};return Ls(()=>{n("getMaterials")}),(r,o)=>(X(),oe("div",hu,[ne(du,{materials:t.value,onApplyMaterial:o[0]||(o[0]=i=>n("applyMaterial",i)),onCreateMaterial:o[1]||(o[1]=i=>n("createMaterial",i)),onDeleteMaterial:o[2]||(o[2]=i=>n("deleteMaterial",i)),onRefreshMaterials:s,onExportMaterial:o[3]||(o[3]=i=>n("exportMaterial",i))},null,8,["materials"])]))}}),gu="modulepreload",mu=function(e,t){return new URL(e,t).href},Cr={},vu=function(t,n,s){let r=Promise.resolve();if(n&&n.length>0){let h=function(u){return Promise.all(u.map(a=>Promise.resolve(a).then(p=>({status:"fulfilled",value:p}),p=>({status:"rejected",reason:p}))))};const i=document.getElementsByTagName("link"),l=document.querySelector("meta[property=csp-nonce]"),c=l?.nonce||l?.getAttribute("nonce");r=h(n.map(u=>{if(u=mu(u,s),u in Cr)return;Cr[u]=!0;const a=u.endsWith(".css"),p=a?'[rel="stylesheet"]':"";if(!!s)for(let P=i.length-1;P>=0;P--){const j=i[P];if(j.href===u&&(!a||j.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${u}"]${p}`))return;const R=document.createElement("link");if(R.rel=a?"stylesheet":gu,a||(R.as="script"),R.crossOrigin="",R.href=u,c&&R.setAttribute("nonce",c),document.head.appendChild(R),a)return new Promise((P,j)=>{R.addEventListener("load",P),R.addEventListener("error",()=>j(new Error(`Unable to preload CSS for ${u}`)))})}))}function o(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return r.then(i=>{for(const l of i||[])l.status==="rejected"&&o(l.reason);return t().catch(o)})};/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Mt=typeof document<"u";function Zo(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function yu(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Zo(e.default)}const K=Object.assign;function rs(e,t){const n={};for(const s in t){const r=t[s];n[s]=Te(r)?r.map(e):e(r)}return n}const sn=()=>{},Te=Array.isArray,Jo=/#/g,bu=/&/g,_u=/\//g,wu=/=/g,xu=/\?/g,Xo=/\+/g,Cu=/%5B/g,Eu=/%5D/g,ei=/%5E/g,Su=/%60/g,ti=/%7B/g,Ru=/%7C/g,ni=/%7D/g,Au=/%20/g;function Ds(e){return encodeURI(""+e).replace(Ru,"|").replace(Cu,"[").replace(Eu,"]")}function Mu(e){return Ds(e).replace(ti,"{").replace(ni,"}").replace(ei,"^")}function bs(e){return Ds(e).replace(Xo,"%2B").replace(Au,"+").replace(Jo,"%23").replace(bu,"%26").replace(Su,"`").replace(ti,"{").replace(ni,"}").replace(ei,"^")}function Pu(e){return bs(e).replace(wu,"%3D")}function Ou(e){return Ds(e).replace(Jo,"%23").replace(xu,"%3F")}function Tu(e){return e==null?"":Ou(e).replace(_u,"%2F")}function hn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const $u=/\/$/,Iu=e=>e.replace($u,"");function os(e,t,n="/"){let s,r={},o="",i="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),o=t.slice(c+1,l>-1?l:t.length),r=e(o)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=zu(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:hn(i)}}function Hu(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Er(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Lu(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Dt(t.matched[s],n.matched[r])&&si(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Dt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function si(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!ku(e[n],t[n]))return!1;return!0}function ku(e,t){return Te(e)?Sr(e,t):Te(t)?Sr(t,e):e===t}function Sr(e,t){return Te(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function zu(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const it={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var pn;(function(e){e.pop="pop",e.push="push"})(pn||(pn={}));var rn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(rn||(rn={}));function Vu(e){if(!e)if(Mt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Iu(e)}const ju=/^[^#]+#/;function Du(e,t){return e.replace(ju,"#")+t}function Nu(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Wn=()=>({left:window.scrollX,top:window.scrollY});function Fu(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=Nu(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Rr(e,t){return(history.state?history.state.position-t:-1)+e}const _s=new Map;function Bu(e,t){_s.set(e,t)}function Uu(e){const t=_s.get(e);return _s.delete(e),t}let Ku=()=>location.protocol+"//"+location.host;function ri(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,c=r.slice(l);return c[0]!=="/"&&(c="/"+c),Er(c,"")}return Er(n,e)+s+r}function Wu(e,t,n,s){let r=[],o=[],i=null;const l=({state:p})=>{const m=ri(e,location),R=n.value,P=t.value;let j=0;if(p){if(n.value=m,t.value=p,i&&i===R){i=null;return}j=P?p.position-P.position:0}else s(m);r.forEach(k=>{k(n.value,R,{delta:j,type:pn.pop,direction:j?j>0?rn.forward:rn.back:rn.unknown})})};function c(){i=n.value}function h(p){r.push(p);const m=()=>{const R=r.indexOf(p);R>-1&&r.splice(R,1)};return o.push(m),m}function u(){const{history:p}=window;p.state&&p.replaceState(K({},p.state,{scroll:Wn()}),"")}function a(){for(const p of o)p();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:c,listen:h,destroy:a}}function Ar(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?Wn():null}}function qu(e){const{history:t,location:n}=window,s={value:ri(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(c,h,u){const a=e.indexOf("#"),p=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+c:Ku()+e+c;try{t[u?"replaceState":"pushState"](h,"",p),r.value=h}catch(m){console.error(m),n[u?"replace":"assign"](p)}}function i(c,h){const u=K({},t.state,Ar(r.value.back,c,r.value.forward,!0),h,{position:r.value.position});o(c,u,!0),s.value=c}function l(c,h){const u=K({},r.value,t.state,{forward:c,scroll:Wn()});o(u.current,u,!0);const a=K({},Ar(s.value,c,null),{position:u.position+1},h);o(c,a,!1),s.value=c}return{location:s,state:r,push:l,replace:i}}function Gu(e){e=Vu(e);const t=qu(e),n=Wu(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=K({location:"",base:e,go:s,createHref:Du.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Yu(e){return typeof e=="string"||e&&typeof e=="object"}function oi(e){return typeof e=="string"||typeof e=="symbol"}const ii=Symbol("");var Mr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Mr||(Mr={}));function Nt(e,t){return K(new Error,{type:e,[ii]:!0},t)}function qe(e,t){return e instanceof Error&&ii in e&&(t==null||!!(e.type&t))}const Pr="[^/]+?",Qu={sensitive:!1,strict:!1,start:!0,end:!0},Zu=/[.+*?^${}()[\]/\\]/g;function Ju(e,t){const n=K({},Qu,t),s=[];let r=n.start?"^":"";const o=[];for(const h of e){const u=h.length?[]:[90];n.strict&&!h.length&&(r+="/");for(let a=0;a<h.length;a++){const p=h[a];let m=40+(n.sensitive?.25:0);if(p.type===0)a||(r+="/"),r+=p.value.replace(Zu,"\\$&"),m+=40;else if(p.type===1){const{value:R,repeatable:P,optional:j,regexp:k}=p;o.push({name:R,repeatable:P,optional:j});const I=k||Pr;if(I!==Pr){m+=10;try{new RegExp(`(${I})`)}catch(T){throw new Error(`Invalid custom RegExp for param "${R}" (${I}): `+T.message)}}let z=P?`((?:${I})(?:/(?:${I}))*)`:`(${I})`;a||(z=j&&h.length<2?`(?:/${z})`:"/"+z),j&&(z+="?"),r+=z,m+=20,j&&(m+=-8),P&&(m+=-20),I===".*"&&(m+=-50)}u.push(m)}s.push(u)}if(n.strict&&n.end){const h=s.length-1;s[h][s[h].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function l(h){const u=h.match(i),a={};if(!u)return null;for(let p=1;p<u.length;p++){const m=u[p]||"",R=o[p-1];a[R.name]=m&&R.repeatable?m.split("/"):m}return a}function c(h){let u="",a=!1;for(const p of e){(!a||!u.endsWith("/"))&&(u+="/"),a=!1;for(const m of p)if(m.type===0)u+=m.value;else if(m.type===1){const{value:R,repeatable:P,optional:j}=m,k=R in h?h[R]:"";if(Te(k)&&!P)throw new Error(`Provided param "${R}" is an array but it is not repeatable (* or + modifiers)`);const I=Te(k)?k.join("/"):k;if(!I)if(j)p.length<2&&(u.endsWith("/")?u=u.slice(0,-1):a=!0);else throw new Error(`Missing required param "${R}"`);u+=I}}return u||"/"}return{re:i,score:s,keys:o,parse:l,stringify:c}}function Xu(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function li(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=Xu(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(Or(s))return 1;if(Or(r))return-1}return r.length-s.length}function Or(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ea={type:0,value:""},ta=/[a-zA-Z0-9_]/;function na(e){if(!e)return[[]];if(e==="/")return[[ea]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${h}": ${m}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,c,h="",u="";function a(){h&&(n===0?o.push({type:0,value:h}):n===1||n===2||n===3?(o.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${h}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:h,regexp:u,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),h="")}function p(){h+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(h&&a(),i()):c===":"?(a(),n=1):p();break;case 4:p(),n=s;break;case 1:c==="("?n=2:ta.test(c)?p():(a(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+c:n=3:u+=c;break;case 3:a(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${h}"`),a(),i(),r}function sa(e,t,n){const s=Ju(na(e.path),n),r=K(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function ra(e,t){const n=[],s=new Map;t=Hr({strict:!1,end:!0,sensitive:!1},t);function r(a){return s.get(a)}function o(a,p,m){const R=!m,P=$r(a);P.aliasOf=m&&m.record;const j=Hr(t,a),k=[P];if("alias"in a){const T=typeof a.alias=="string"?[a.alias]:a.alias;for(const Q of T)k.push($r(K({},P,{components:m?m.record.components:P.components,path:Q,aliasOf:m?m.record:P})))}let I,z;for(const T of k){const{path:Q}=T;if(p&&Q[0]!=="/"){const ce=p.record.path,re=ce[ce.length-1]==="/"?"":"/";T.path=p.record.path+(Q&&re+Q)}if(I=sa(T,p,j),m?m.alias.push(I):(z=z||I,z!==I&&z.alias.push(I),R&&a.name&&!Ir(I)&&i(a.name)),ci(I)&&c(I),P.children){const ce=P.children;for(let re=0;re<ce.length;re++)o(ce[re],I,m&&m.children[re])}m=m||I}return z?()=>{i(z)}:sn}function i(a){if(oi(a)){const p=s.get(a);p&&(s.delete(a),n.splice(n.indexOf(p),1),p.children.forEach(i),p.alias.forEach(i))}else{const p=n.indexOf(a);p>-1&&(n.splice(p,1),a.record.name&&s.delete(a.record.name),a.children.forEach(i),a.alias.forEach(i))}}function l(){return n}function c(a){const p=la(a,n);n.splice(p,0,a),a.record.name&&!Ir(a)&&s.set(a.record.name,a)}function h(a,p){let m,R={},P,j;if("name"in a&&a.name){if(m=s.get(a.name),!m)throw Nt(1,{location:a});j=m.record.name,R=K(Tr(p.params,m.keys.filter(z=>!z.optional).concat(m.parent?m.parent.keys.filter(z=>z.optional):[]).map(z=>z.name)),a.params&&Tr(a.params,m.keys.map(z=>z.name))),P=m.stringify(R)}else if(a.path!=null)P=a.path,m=n.find(z=>z.re.test(P)),m&&(R=m.parse(P),j=m.record.name);else{if(m=p.name?s.get(p.name):n.find(z=>z.re.test(p.path)),!m)throw Nt(1,{location:a,currentLocation:p});j=m.record.name,R=K({},p.params,a.params),P=m.stringify(R)}const k=[];let I=m;for(;I;)k.unshift(I.record),I=I.parent;return{name:j,path:P,params:R,matched:k,meta:ia(k)}}e.forEach(a=>o(a));function u(){n.length=0,s.clear()}return{addRoute:o,resolve:h,removeRoute:i,clearRoutes:u,getRoutes:l,getRecordMatcher:r}}function Tr(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function $r(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:oa(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function oa(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Ir(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function ia(e){return e.reduce((t,n)=>K(t,n.meta),{})}function Hr(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function la(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;li(e,t[o])<0?s=o:n=o+1}const r=ca(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function ca(e){let t=e;for(;t=t.parent;)if(ci(t)&&li(e,t)===0)return t}function ci({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function ua(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(Xo," "),i=o.indexOf("="),l=hn(i<0?o:o.slice(0,i)),c=i<0?null:hn(o.slice(i+1));if(l in t){let h=t[l];Te(h)||(h=t[l]=[h]),h.push(c)}else t[l]=c}return t}function Lr(e){let t="";for(let n in e){const s=e[n];if(n=Pu(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Te(s)?s.map(o=>o&&bs(o)):[s&&bs(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function aa(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Te(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const fa=Symbol(""),kr=Symbol(""),Ns=Symbol(""),ui=Symbol(""),ws=Symbol("");function Wt(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function ut(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,c)=>{const h=p=>{p===!1?c(Nt(4,{from:n,to:t})):p instanceof Error?c(p):Yu(p)?c(Nt(2,{from:t,to:p})):(i&&s.enterCallbacks[r]===i&&typeof p=="function"&&i.push(p),l())},u=o(()=>e.call(s&&s.instances[r],t,n,h));let a=Promise.resolve(u);e.length<3&&(a=a.then(h)),a.catch(p=>c(p))})}function is(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let c=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(Zo(c)){const u=(c.__vccOpts||c)[t];u&&o.push(ut(u,n,s,i,l,r))}else{let h=c();o.push(()=>h.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const a=yu(u)?u.default:u;i.mods[l]=u,i.components[l]=a;const m=(a.__vccOpts||a)[t];return m&&ut(m,n,s,i,l,r)()}))}}return o}function zr(e){const t=Ze(Ns),n=Ze(ui),s=Ce(()=>{const c=$t(e.to);return t.resolve(c)}),r=Ce(()=>{const{matched:c}=s.value,{length:h}=c,u=c[h-1],a=n.matched;if(!u||!a.length)return-1;const p=a.findIndex(Dt.bind(null,u));if(p>-1)return p;const m=Vr(c[h-2]);return h>1&&Vr(u)===m&&a[a.length-1].path!==m?a.findIndex(Dt.bind(null,c[h-2])):p}),o=Ce(()=>r.value>-1&&ma(n.params,s.value.params)),i=Ce(()=>r.value>-1&&r.value===n.matched.length-1&&si(n.params,s.value.params));function l(c={}){if(ga(c)){const h=t[$t(e.replace)?"replace":"push"]($t(e.to)).catch(sn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>h),h}return Promise.resolve()}return{route:s,href:Ce(()=>s.value.href),isActive:o,isExactActive:i,navigate:l}}function da(e){return e.length===1?e[0]:e}const ha=ht({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:zr,setup(e,{slots:t}){const n=Dn(zr(e)),{options:s}=Ze(Ns),r=Ce(()=>({[jr(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[jr(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&da(t.default(n));return e.custom?o:qo("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),pa=ha;function ga(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function ma(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!Te(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function Vr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const jr=(e,t,n)=>e??t??n,va=ht({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=Ze(ws),r=Ce(()=>e.route||s.value),o=Ze(kr,0),i=Ce(()=>{let h=$t(o);const{matched:u}=r.value;let a;for(;(a=u[h])&&!a.components;)h++;return h}),l=Ce(()=>r.value.matched[i.value]);Cn(kr,Ce(()=>i.value+1)),Cn(fa,l),Cn(ws,r);const c=Be();return kt(()=>[c.value,l.value,e.name],([h,u,a],[p,m,R])=>{u&&(u.instances[a]=h,m&&m!==u&&h&&h===p&&(u.leaveGuards.size||(u.leaveGuards=m.leaveGuards),u.updateGuards.size||(u.updateGuards=m.updateGuards))),h&&u&&(!m||!Dt(u,m)||!p)&&(u.enterCallbacks[a]||[]).forEach(P=>P(h))},{flush:"post"}),()=>{const h=r.value,u=e.name,a=l.value,p=a&&a.components[u];if(!p)return Dr(n.default,{Component:p,route:h});const m=a.props[u],R=m?m===!0?h.params:typeof m=="function"?m(h):m:null,j=qo(p,K({},R,t,{onVnodeUnmounted:k=>{k.component.isUnmounted&&(a.instances[u]=null)},ref:c}));return Dr(n.default,{Component:j,route:h})||j}}});function Dr(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const ya=va;function ba(e){const t=ra(e.routes,e),n=e.parseQuery||ua,s=e.stringifyQuery||Lr,r=e.history,o=Wt(),i=Wt(),l=Wt(),c=Fi(it);let h=it;Mt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=rs.bind(null,y=>""+y),a=rs.bind(null,Tu),p=rs.bind(null,hn);function m(y,O){let A,$;return oi(y)?(A=t.getRecordMatcher(y),$=O):$=y,t.addRoute($,A)}function R(y){const O=t.getRecordMatcher(y);O&&t.removeRoute(O)}function P(){return t.getRoutes().map(y=>y.record)}function j(y){return!!t.getRecordMatcher(y)}function k(y,O){if(O=K({},O||c.value),typeof y=="string"){const g=os(n,y,O.path),v=t.resolve({path:g.path},O),_=r.createHref(g.fullPath);return K(g,v,{params:p(v.params),hash:hn(g.hash),redirectedFrom:void 0,href:_})}let A;if(y.path!=null)A=K({},y,{path:os(n,y.path,O.path).path});else{const g=K({},y.params);for(const v in g)g[v]==null&&delete g[v];A=K({},y,{params:a(g)}),O.params=a(O.params)}const $=t.resolve(A,O),Z=y.hash||"";$.params=u(p($.params));const f=Hu(s,K({},y,{hash:Mu(Z),path:$.path})),d=r.createHref(f);return K({fullPath:f,hash:Z,query:s===Lr?aa(y.query):y.query||{}},$,{redirectedFrom:void 0,href:d})}function I(y){return typeof y=="string"?os(n,y,c.value.path):K({},y)}function z(y,O){if(h!==y)return Nt(8,{from:O,to:y})}function T(y){return re(y)}function Q(y){return T(K(I(y),{replace:!0}))}function ce(y){const O=y.matched[y.matched.length-1];if(O&&O.redirect){const{redirect:A}=O;let $=typeof A=="function"?A(y):A;return typeof $=="string"&&($=$.includes("?")||$.includes("#")?$=I($):{path:$},$.params={}),K({query:y.query,hash:y.hash,params:$.path!=null?{}:y.params},$)}}function re(y,O){const A=h=k(y),$=c.value,Z=y.state,f=y.force,d=y.replace===!0,g=ce(A);if(g)return re(K(I(g),{state:typeof g=="object"?K({},Z,g.state):Z,force:f,replace:d}),O||A);const v=A;v.redirectedFrom=O;let _;return!f&&Lu(s,$,A)&&(_=Nt(16,{to:v,from:$}),Le($,$,!0,!1)),(_?Promise.resolve(_):Ie(v,$)).catch(b=>qe(b)?qe(b,2)?b:ot(b):U(b,v,$)).then(b=>{if(b){if(qe(b,2))return re(K({replace:d},I(b.to),{state:typeof b.to=="object"?K({},Z,b.to.state):Z,force:f}),O||v)}else b=pt(v,$,!0,d,Z);return rt(v,$,b),b})}function $e(y,O){const A=z(y,O);return A?Promise.reject(A):Promise.resolve()}function st(y){const O=Et.values().next().value;return O&&typeof O.runWithContext=="function"?O.runWithContext(y):y()}function Ie(y,O){let A;const[$,Z,f]=_a(y,O);A=is($.reverse(),"beforeRouteLeave",y,O);for(const g of $)g.leaveGuards.forEach(v=>{A.push(ut(v,y,O))});const d=$e.bind(null,y,O);return A.push(d),Ae(A).then(()=>{A=[];for(const g of o.list())A.push(ut(g,y,O));return A.push(d),Ae(A)}).then(()=>{A=is(Z,"beforeRouteUpdate",y,O);for(const g of Z)g.updateGuards.forEach(v=>{A.push(ut(v,y,O))});return A.push(d),Ae(A)}).then(()=>{A=[];for(const g of f)if(g.beforeEnter)if(Te(g.beforeEnter))for(const v of g.beforeEnter)A.push(ut(v,y,O));else A.push(ut(g.beforeEnter,y,O));return A.push(d),Ae(A)}).then(()=>(y.matched.forEach(g=>g.enterCallbacks={}),A=is(f,"beforeRouteEnter",y,O,st),A.push(d),Ae(A))).then(()=>{A=[];for(const g of i.list())A.push(ut(g,y,O));return A.push(d),Ae(A)}).catch(g=>qe(g,8)?g:Promise.reject(g))}function rt(y,O,A){l.list().forEach($=>st(()=>$(y,O,A)))}function pt(y,O,A,$,Z){const f=z(y,O);if(f)return f;const d=O===it,g=Mt?history.state:{};A&&($||d?r.replace(y.fullPath,K({scroll:d&&g&&g.scroll},Z)):r.push(y.fullPath,Z)),c.value=y,Le(y,O,A,d),ot()}let He;function Ft(){He||(He=r.listen((y,O,A)=>{if(!yn.listening)return;const $=k(y),Z=ce($);if(Z){re(K(Z,{replace:!0,force:!0}),$).catch(sn);return}h=$;const f=c.value;Mt&&Bu(Rr(f.fullPath,A.delta),Wn()),Ie($,f).catch(d=>qe(d,12)?d:qe(d,2)?(re(K(I(d.to),{force:!0}),$).then(g=>{qe(g,20)&&!A.delta&&A.type===pn.pop&&r.go(-1,!1)}).catch(sn),Promise.reject()):(A.delta&&r.go(-A.delta,!1),U(d,$,f))).then(d=>{d=d||pt($,f,!1),d&&(A.delta&&!qe(d,8)?r.go(-A.delta,!1):A.type===pn.pop&&qe(d,20)&&r.go(-1,!1)),rt($,f,d)}).catch(sn)}))}let xt=Wt(),le=Wt(),Y;function U(y,O,A){ot(y);const $=le.list();return $.length?$.forEach(Z=>Z(y,O,A)):console.error(y),Promise.reject(y)}function Ke(){return Y&&c.value!==it?Promise.resolve():new Promise((y,O)=>{xt.add([y,O])})}function ot(y){return Y||(Y=!y,Ft(),xt.list().forEach(([O,A])=>y?A(y):O()),xt.reset()),y}function Le(y,O,A,$){const{scrollBehavior:Z}=e;if(!Mt||!Z)return Promise.resolve();const f=!A&&Uu(Rr(y.fullPath,0))||($||!A)&&history.state&&history.state.scroll||null;return vo().then(()=>Z(y,O,f)).then(d=>d&&Fu(d)).catch(d=>U(d,y,O))}const be=y=>r.go(y);let Ct;const Et=new Set,yn={currentRoute:c,listening:!0,addRoute:m,removeRoute:R,clearRoutes:t.clearRoutes,hasRoute:j,getRoutes:P,resolve:k,options:e,push:T,replace:Q,go:be,back:()=>be(-1),forward:()=>be(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:le.add,isReady:Ke,install(y){const O=this;y.component("RouterLink",pa),y.component("RouterView",ya),y.config.globalProperties.$router=O,Object.defineProperty(y.config.globalProperties,"$route",{enumerable:!0,get:()=>$t(c)}),Mt&&!Ct&&c.value===it&&(Ct=!0,T(r.location).catch(Z=>{}));const A={};for(const Z in it)Object.defineProperty(A,Z,{get:()=>c.value[Z],enumerable:!0});y.provide(Ns,O),y.provide(ui,ao(A)),y.provide(ws,c);const $=y.unmount;Et.add(y),y.unmount=function(){Et.delete(y),Et.size<1&&(h=it,He&&He(),He=null,c.value=it,Ct=!1,Y=!1),$()}}};function Ae(y){return y.reduce((O,A)=>O.then(()=>st(A)),Promise.resolve())}return yn}function _a(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(h=>Dt(h,l))?s.push(l):n.push(l));const c=e.matched[i];c&&(t.matched.find(h=>Dt(h,c))||r.push(c))}return[n,s,r]}const wa={},xa={class:"item"},Ca={class:"details"};function Ea(e,t){return X(),oe("div",xa,[w("i",null,[Jn(e.$slots,"icon",{},void 0)]),w("div",Ca,[w("h3",null,[Jn(e.$slots,"heading",{},void 0)]),Jn(e.$slots,"default",{},void 0)])])}const qt=nt(wa,[["render",Ea],["__scopeId","data-v-fd0742eb"]]),Sa={},Ra={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"17",fill:"currentColor"};function Aa(e,t){return X(),oe("svg",Ra,t[0]||(t[0]=[w("path",{d:"M11 2.253a1 1 0 1 0-2 0h2zm-2 13a1 1 0 1 0 2 0H9zm.447-12.167a1 1 0 1 0 1.107-1.666L9.447 3.086zM1 2.253L.447 1.42A1 1 0 0 0 0 2.253h1zm0 13H0a1 1 0 0 0 1.553.833L1 15.253zm8.447.833a1 1 0 1 0 1.107-1.666l-1.107 1.666zm0-14.666a1 1 0 1 0 1.107 1.666L9.447 1.42zM19 2.253h1a1 1 0 0 0-.447-.833L19 2.253zm0 13l-.553.833A1 1 0 0 0 20 15.253h-1zm-9.553-.833a1 1 0 1 0 1.107 1.666L9.447 14.42zM9 2.253v13h2v-13H9zm1.553-.833C9.203.523 7.42 0 5.5 0v2c1.572 0 2.961.431 3.947 1.086l1.107-1.666zM5.5 0C3.58 0 1.797.523.447 1.42l1.107 1.666C2.539 2.431 3.928 2 5.5 2V0zM0 2.253v13h2v-13H0zm1.553 13.833C2.539 15.431 3.928 15 5.5 15v-2c-1.92 0-3.703.523-5.053 1.42l1.107 1.666zM5.5 15c1.572 0 2.961.431 3.947 1.086l1.107-1.666C9.203 13.523 7.42 13 5.5 13v2zm5.053-11.914C11.539 2.431 12.928 2 14.5 2V0c-1.92 0-3.703.523-5.053 1.42l1.107 1.666zM14.5 2c1.573 0 2.961.431 3.947 1.086l1.107-1.666C18.203.523 16.421 0 14.5 0v2zm3.5.253v13h2v-13h-2zm1.553 12.167C18.203 13.523 16.421 13 14.5 13v2c1.573 0 2.961.431 3.947 1.086l1.107-1.666zM14.5 13c-1.92 0-3.703.523-5.053 1.42l1.107 1.666C11.539 15.431 12.928 15 14.5 15v-2z"},null,-1)]))}const Ma=nt(Sa,[["render",Aa]]),Pa={},Oa={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink","aria-hidden":"true",role:"img",class:"iconify iconify--mdi",width:"24",height:"24",preserveAspectRatio:"xMidYMid meet",viewBox:"0 0 24 24"};function Ta(e,t){return X(),oe("svg",Oa,t[0]||(t[0]=[w("path",{d:"M20 18v-4h-3v1h-2v-1H9v1H7v-1H4v4h16M6.33 8l-1.74 4H7v-1h2v1h6v-1h2v1h2.41l-1.74-4H6.33M9 5v1h6V5H9m12.84 7.61c.1.22.16.48.16.8V18c0 .53-.21 1-.6 1.41c-.4.4-.85.59-1.4.59H4c-.55 0-1-.19-1.4-.59C2.21 19 2 18.53 2 18v-4.59c0-.32.06-.58.16-.8L4.5 7.22C4.84 6.41 5.45 6 6.33 6H7V5c0-.55.18-1 .57-1.41C7.96 3.2 8.44 3 9 3h6c.56 0 1.04.2 1.43.59c.39.41.57.86.57 1.41v1h.67c.88 0 1.49.41 1.83 1.22l2.34 5.39z",fill:"currentColor"},null,-1)]))}const $a=nt(Pa,[["render",Ta]]),Ia={},Ha={xmlns:"http://www.w3.org/2000/svg",width:"18",height:"20",fill:"currentColor"};function La(e,t){return X(),oe("svg",Ha,t[0]||(t[0]=[w("path",{d:"M11.447 8.894a1 1 0 1 0-.894-1.789l.894 1.789zm-2.894-.789a1 1 0 1 0 .894 1.789l-.894-1.789zm0 1.789a1 1 0 1 0 .894-1.789l-.894 1.789zM7.447 7.106a1 1 0 1 0-.894 1.789l.894-1.789zM10 9a1 1 0 1 0-2 0h2zm-2 2.5a1 1 0 1 0 2 0H8zm9.447-5.606a1 1 0 1 0-.894-1.789l.894 1.789zm-2.894-.789a1 1 0 1 0 .894 1.789l-.894-1.789zm2 .789a1 1 0 1 0 .894-1.789l-.894 1.789zm-1.106-2.789a1 1 0 1 0-.894 1.789l.894-1.789zM18 5a1 1 0 1 0-2 0h2zm-2 2.5a1 1 0 1 0 2 0h-2zm-5.447-4.606a1 1 0 1 0 .894-1.789l-.894 1.789zM9 1l.447-.894a1 1 0 0 0-.894 0L9 1zm-2.447.106a1 1 0 1 0 .894 1.789l-.894-1.789zm-6 3a1 1 0 1 0 .894 1.789L.553 4.106zm2.894.789a1 1 0 1 0-.894-1.789l.894 1.789zm-2-.789a1 1 0 1 0-.894 1.789l.894-1.789zm1.106 2.789a1 1 0 1 0 .894-1.789l-.894 1.789zM2 5a1 1 0 1 0-2 0h2zM0 7.5a1 1 0 1 0 2 0H0zm8.553 12.394a1 1 0 1 0 .894-1.789l-.894 1.789zm-1.106-2.789a1 1 0 1 0-.894 1.789l.894-1.789zm1.106 1a1 1 0 1 0 .894 1.789l-.894-1.789zm2.894.789a1 1 0 1 0-.894-1.789l.894 1.789zM8 19a1 1 0 1 0 2 0H8zm2-2.5a1 1 0 1 0-2 0h2zm-7.447.394a1 1 0 1 0 .894-1.789l-.894 1.789zM1 15H0a1 1 0 0 0 .553.894L1 15zm1-2.5a1 1 0 1 0-2 0h2zm12.553 2.606a1 1 0 1 0 .894 1.789l-.894-1.789zM17 15l.447.894A1 1 0 0 0 18 15h-1zm1-2.5a1 1 0 1 0-2 0h2zm-7.447-5.394l-2 1 .894 1.789 2-1-.894-1.789zm-1.106 1l-2-1-.894 1.789 2 1 .894-1.789zM8 9v2.5h2V9H8zm8.553-4.894l-2 1 .894 1.789 2-1-.894-1.789zm.894 0l-2-1-.894 1.789 2 1 .894-1.789zM16 5v2.5h2V5h-2zm-4.553-3.894l-2-1-.894 1.789 2 1 .894-1.789zm-2.894-1l-2 1 .894 1.789 2-1L8.553.106zM1.447 5.894l2-1-.894-1.789-2 1 .894 1.789zm-.894 0l2 1 .894-1.789-2-1-.894 1.789zM0 5v2.5h2V5H0zm9.447 13.106l-2-1-.894 1.789 2 1 .894-1.789zm0 1.789l2-1-.894-1.789-2 1 .894 1.789zM10 19v-2.5H8V19h2zm-6.553-3.894l-2-1-.894 1.789 2 1 .894-1.789zM2 15v-2.5H0V15h2zm13.447 1.894l2-1-.894-1.789-2 1 .894 1.789zM18 15v-2.5h-2V15h2z"},null,-1)]))}const ka=nt(Ia,[["render",La]]),za={},Va={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor"};function ja(e,t){return X(),oe("svg",Va,t[0]||(t[0]=[w("path",{d:"M15 4a1 1 0 1 0 0 2V4zm0 11v-1a1 1 0 0 0-1 1h1zm0 4l-.707.707A1 1 0 0 0 16 19h-1zm-4-4l.707-.707A1 1 0 0 0 11 14v1zm-4.707-1.293a1 1 0 0 0-1.414 1.414l1.414-1.414zm-.707.707l-.707-.707.707.707zM9 11v-1a1 1 0 0 0-.707.293L9 11zm-4 0h1a1 1 0 0 0-1-1v1zm0 4H4a1 1 0 0 0 1.707.707L5 15zm10-9h2V4h-2v2zm2 0a1 1 0 0 1 1 1h2a3 3 0 0 0-3-3v2zm1 1v6h2V7h-2zm0 6a1 1 0 0 1-1 1v2a3 3 0 0 0 3-3h-2zm-1 1h-2v2h2v-2zm-3 1v4h2v-4h-2zm1.707 3.293l-4-4-1.414 1.414 4 4 1.414-1.414zM11 14H7v2h4v-2zm-4 0c-.276 0-.525-.111-.707-.293l-1.414 1.414C5.42 15.663 6.172 16 7 16v-2zm-.707 1.121l3.414-3.414-1.414-1.414-3.414 3.414 1.414 1.414zM9 12h4v-2H9v2zm4 0a3 3 0 0 0 3-3h-2a1 1 0 0 1-1 1v2zm3-3V3h-2v6h2zm0-6a3 3 0 0 0-3-3v2a1 1 0 0 1 1 1h2zm-3-3H3v2h10V0zM3 0a3 3 0 0 0-3 3h2a1 1 0 0 1 1-1V0zM0 3v6h2V3H0zm0 6a3 3 0 0 0 3 3v-2a1 1 0 0 1-1-1H0zm3 3h2v-2H3v2zm1-1v4h2v-4H4zm1.707 4.707l.586-.586-1.414-1.414-.586.586 1.414 1.414z"},null,-1)]))}const Da=nt(za,[["render",ja]]),Na={},Fa={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor"};function Ba(e,t){return X(),oe("svg",Fa,t[0]||(t[0]=[w("path",{d:"M10 3.22l-.61-.6a5.5 5.5 0 0 0-7.666.105 5.5 5.5 0 0 0-.114 7.665L10 18.78l8.39-8.4a5.5 5.5 0 0 0-.114-7.665 5.5 5.5 0 0 0-7.666-.105l-.61.61z"},null,-1)]))}const Ua=nt(Na,[["render",Ba]]),Ka=ht({__name:"TheWelcome",setup(e){const t=()=>fetch("/__open-in-editor?file=README.md");return(n,s)=>(X(),oe(ve,null,[ne(qt,null,{icon:ae(()=>[ne(Ma)]),heading:ae(()=>s[0]||(s[0]=[F("Documentation")])),default:ae(()=>[s[1]||(s[1]=F(" Vue’s ")),s[2]||(s[2]=w("a",{href:"https://vuejs.org/",target:"_blank",rel:"noopener"},"official documentation",-1)),s[3]||(s[3]=F(" provides you with all information you need to get started. "))]),_:1,__:[1,2,3]}),ne(qt,null,{icon:ae(()=>[ne($a)]),heading:ae(()=>s[4]||(s[4]=[F("Tooling")])),default:ae(()=>[s[6]||(s[6]=F(" This project is served and bundled with ")),s[7]||(s[7]=w("a",{href:"https://vite.dev/guide/features.html",target:"_blank",rel:"noopener"},"Vite",-1)),s[8]||(s[8]=F(". The recommended IDE setup is ")),s[9]||(s[9]=w("a",{href:"https://code.visualstudio.com/",target:"_blank",rel:"noopener"},"VSCode",-1)),s[10]||(s[10]=F(" + ")),s[11]||(s[11]=w("a",{href:"https://github.com/vuejs/language-tools",target:"_blank",rel:"noopener"},"Vue - Official",-1)),s[12]||(s[12]=F(". If you need to test your components and web pages, check out ")),s[13]||(s[13]=w("a",{href:"https://vitest.dev/",target:"_blank",rel:"noopener"},"Vitest",-1)),s[14]||(s[14]=F(" and ")),s[15]||(s[15]=w("a",{href:"https://www.cypress.io/",target:"_blank",rel:"noopener"},"Cypress",-1)),s[16]||(s[16]=F(" / ")),s[17]||(s[17]=w("a",{href:"https://playwright.dev/",target:"_blank",rel:"noopener"},"Playwright",-1)),s[18]||(s[18]=F(". ")),s[19]||(s[19]=w("br",null,null,-1)),s[20]||(s[20]=F(" More instructions are available in ")),w("a",{href:"javascript:void(0)",onClick:t},s[5]||(s[5]=[w("code",null,"README.md",-1)])),s[21]||(s[21]=F(". "))]),_:1,__:[6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21]}),ne(qt,null,{icon:ae(()=>[ne(ka)]),heading:ae(()=>s[22]||(s[22]=[F("Ecosystem")])),default:ae(()=>[s[23]||(s[23]=F(" Get official tools and libraries for your project: ")),s[24]||(s[24]=w("a",{href:"https://pinia.vuejs.org/",target:"_blank",rel:"noopener"},"Pinia",-1)),s[25]||(s[25]=F(", ")),s[26]||(s[26]=w("a",{href:"https://router.vuejs.org/",target:"_blank",rel:"noopener"},"Vue Router",-1)),s[27]||(s[27]=F(", ")),s[28]||(s[28]=w("a",{href:"https://test-utils.vuejs.org/",target:"_blank",rel:"noopener"},"Vue Test Utils",-1)),s[29]||(s[29]=F(", and ")),s[30]||(s[30]=w("a",{href:"https://github.com/vuejs/devtools",target:"_blank",rel:"noopener"},"Vue Dev Tools",-1)),s[31]||(s[31]=F(". If you need more resources, we suggest paying ")),s[32]||(s[32]=w("a",{href:"https://github.com/vuejs/awesome-vue",target:"_blank",rel:"noopener"},"Awesome Vue",-1)),s[33]||(s[33]=F(" a visit. "))]),_:1,__:[23,24,25,26,27,28,29,30,31,32,33]}),ne(qt,null,{icon:ae(()=>[ne(Da)]),heading:ae(()=>s[34]||(s[34]=[F("Community")])),default:ae(()=>[s[35]||(s[35]=F(" Got stuck? Ask your question on ")),s[36]||(s[36]=w("a",{href:"https://chat.vuejs.org",target:"_blank",rel:"noopener"},"Vue Land",-1)),s[37]||(s[37]=F(" (our official Discord server), or ")),s[38]||(s[38]=w("a",{href:"https://stackoverflow.com/questions/tagged/vue.js",target:"_blank",rel:"noopener"},"StackOverflow",-1)),s[39]||(s[39]=F(". You should also follow the official ")),s[40]||(s[40]=w("a",{href:"https://bsky.app/profile/vuejs.org",target:"_blank",rel:"noopener"},"@vuejs.org",-1)),s[41]||(s[41]=F(" Bluesky account or the ")),s[42]||(s[42]=w("a",{href:"https://x.com/vuejs",target:"_blank",rel:"noopener"},"@vuejs",-1)),s[43]||(s[43]=F(" X account for latest news in the Vue world. "))]),_:1,__:[35,36,37,38,39,40,41,42,43]}),ne(qt,null,{icon:ae(()=>[ne(Ua)]),heading:ae(()=>s[44]||(s[44]=[F("Support Vue")])),default:ae(()=>[s[45]||(s[45]=F(" As an independent project, Vue relies on community backing for its sustainability. You can help us by ")),s[46]||(s[46]=w("a",{href:"https://vuejs.org/sponsor/",target:"_blank",rel:"noopener"},"becoming a sponsor",-1)),s[47]||(s[47]=F(". "))]),_:1,__:[45,46,47]})],64))}}),Wa=ht({__name:"HomeView",setup(e){return(t,n)=>(X(),oe("main",null,[ne(Ka)]))}}),qa=ba({history:Gu("./"),routes:[{path:"/",name:"home",component:Wa},{path:"/about",name:"about",component:()=>vu(()=>import("./AboutView-DB32AxCe.js"),__vite__mapDeps([0,1]),import.meta.url)}]}),Fs=Sc(pu);Fs.use(Pc());Fs.use(qa);Fs.mount("#app");export{nt as _,w as a,oe as c,X as o};
