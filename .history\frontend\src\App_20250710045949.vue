<script setup lang="ts">
import { onMounted, ref } from 'vue'
import MaterialBrowser from './components/MaterialBrowser.vue'

// 声明全局函数供Ruby调用
declare global {
  interface Window {
    receiveMaterials: (materials: any[]) => void
    sketchup?: any
  }
}

const materials = ref<any[]>([])

// 接收来自Ruby的材质数据
window.receiveMaterials = (materialsData: any[]) => {
  materials.value = materialsData
}

// 向Ruby发送消息的辅助函数
const sendToRuby = (action: string, data?: any) => {
  console.log('Sending to Ruby:', action, data)
  console.log('window.sketchup:', window.sketchup)
  console.log('window.sketchup.callback type:', typeof window.sketchup?.callback)

  if (window.sketchup && typeof window.sketchup.callback === 'function') {
    try {
      console.log('Calling SketchUp callback...')
      window.sketchup.callback(action, data)
      console.log('SketchUp callback completed')
    } catch (error) {
      console.error('Error calling Ruby:', error)
    }
  } else {
    console.warn('SketchUp bridge not available')
    console.log('window.sketchup exists:', !!window.sketchup)
    console.log('callback function exists:', !!(window.sketchup && window.sketchup.callback))
    console.log('callback type:', typeof window.sketchup?.callback)

    // 模拟数据用于测试
    if (action === 'getMaterials' || action === 'refreshMaterials') {
      console.log('Using mock data for testing')
      setTimeout(() => {
        const mockMaterials = [
          {
            name: "Default",
            display_name: "默认材质",
            color: [0.8, 0.8, 0.8],
            alpha: 1,
            texture: null,
            texture_path: null,
            icon_data: null,
            use_alpha: false
          }
        ]
        window.receiveMaterials && window.receiveMaterials(mockMaterials)
      }, 100)
    }
  }
}

// 刷新材质列表
const refreshMaterials = () => {
  sendToRuby('refreshMaterials')
}

// 检查SketchUp桥接是否准备好
const checkSketchUpBridge = (retries = 0) => {
  console.log(`Checking SketchUp bridge (attempt ${retries + 1})...`)
  console.log('window.sketchup:', window.sketchup)
  console.log('callback function:', window.sketchup?.callback)

  if (window.sketchup && typeof window.sketchup.callback === 'function') {
    console.log('SketchUp bridge is ready, requesting materials...')
    sendToRuby('getMaterials')
  } else if (retries < 10) {
    console.log('SketchUp bridge not ready, retrying in 1 second...')
    setTimeout(() => checkSketchUpBridge(retries + 1), 1000)
  } else {
    console.error('SketchUp bridge failed to initialize after 10 attempts')
    // 使用模拟数据
    sendToRuby('getMaterials')
  }
}

onMounted(() => {
  console.log('App mounted, starting SketchUp bridge check...')

  // 立即检查一次，然后开始重试机制
  setTimeout(() => checkSketchUpBridge(), 100)
})
</script>

<template>
  <div id="app">
    <MaterialBrowser
      :materials="materials"
      @apply-material="(name) => sendToRuby('applyMaterial', name)"
      @create-material="(data) => sendToRuby('createMaterial', data)"
      @delete-material="(name) => sendToRuby('deleteMaterial', name)"
      @refresh-materials="refreshMaterials"
      @export-material="(name) => sendToRuby('exportMaterial', name)"
    />
  </div>
</template>

<style>
/* 全局样式已在main.css中定义 */
</style>
