# encoding: utf-8
# 简单的测试脚本

puts "重新加载材质浏览器插件..."

begin
  # 重新加载插件
  load 'D:/115/材质浏览器/plugin/material_browser.rb'
  puts "✅ 插件加载成功"
  
  # 检查材质数量
  model = Sketchup.active_model
  materials_count = model.materials.length
  puts "当前模型有 #{materials_count} 个材质"
  
  # 打开材质浏览器
  puts "打开材质浏览器..."
  MaterialBrowser.show_material_browser
  
  puts "\n请检查:"
  puts "1. 材质浏览器窗口是否打开"
  puts "2. 浏览器控制台 (F12) 中的日志信息"
  puts "3. 是否显示了 #{materials_count} 个材质"
  puts "4. 刷新按钮是否正常工作"
  
rescue => e
  puts "❌ 错误: #{e.message}"
  puts e.backtrace.first
end
