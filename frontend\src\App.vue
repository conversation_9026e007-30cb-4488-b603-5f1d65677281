<script setup lang="ts">
import { onMounted, ref } from 'vue'
import MaterialBrowser from './components/MaterialBrowser.vue'

// 声明全局函数供Ruby调用
declare global {
  interface Window {
    receiveMaterials: (materials: any[]) => void
    sketchup?: any
  }
}

const materials = ref<any[]>([])

// 接收来自Ruby的材质数据
window.receiveMaterials = (materialsData: any[]) => {
  materials.value = materialsData
}

// 向Ruby发送消息的辅助函数
const sendToRuby = (action: string, data?: any) => {
  console.log('Sending to Ruby:', action, data)

  if (window.sketchup && typeof window.sketchup.callback === 'function') {
    try {
      window.sketchup.callback(action, data)
    } catch (error) {
      console.error('Error calling Ruby:', error)
    }
  } else {
    console.warn('SketchUp bridge not available, action:', action, data)

    // 模拟数据用于测试
    if (action === 'getMaterials' || action === 'refreshMaterials') {
      setTimeout(() => {
        const mockMaterials = [
          {
            name: "Default",
            display_name: "默认材质",
            color: [0.8, 0.8, 0.8],
            alpha: 1,
            texture: null,
            texture_path: null,
            icon_data: null,
            use_alpha: false
          }
        ]
        window.receiveMaterials && window.receiveMaterials(mockMaterials)
      }, 100)
    }
  }
}

// 刷新材质列表
const refreshMaterials = () => {
  sendToRuby('refreshMaterials')
}

onMounted(() => {
  console.log('App mounted, checking SketchUp bridge...')
  console.log('window.sketchup:', window.sketchup)

  // 延迟请求材质数据，给SketchUp时间设置桥接
  setTimeout(() => {
    console.log('Requesting materials...')
    sendToRuby('getMaterials')
  }, 500)
})
</script>

<template>
  <div id="app">
    <MaterialBrowser
      :materials="materials"
      @apply-material="(name) => sendToRuby('applyMaterial', name)"
      @create-material="(data) => sendToRuby('createMaterial', data)"
      @delete-material="(name) => sendToRuby('deleteMaterial', name)"
      @refresh-materials="refreshMaterials"
      @export-material="(name) => sendToRuby('exportMaterial', name)"
    />
  </div>
</template>

<style>
/* 全局样式已在main.css中定义 */
</style>
