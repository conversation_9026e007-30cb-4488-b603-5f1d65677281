# encoding: utf-8
# 完整的重新加载和测试脚本

puts "=" * 50
puts "材质浏览器 - 完整测试脚本"
puts "=" * 50

begin
  # 1. 创建测试材质
  puts "\n1. 创建测试材质..."
  model = Sketchup.active_model
  materials = model.materials
  
  # 创建一些测试材质（如果不存在）
  test_materials = [
    { name: "测试红色", color: [255, 0, 0], alpha: 1.0 },
    { name: "测试蓝色", color: [0, 0, 255], alpha: 0.7 },
    { name: "测试绿色", color: [0, 255, 0], alpha: 1.0 },
    { name: "测试黄色", color: [255, 255, 0], alpha: 0.8 }
  ]
  
  test_materials.each do |mat_info|
    unless materials[mat_info[:name]]
      material = materials.add(mat_info[:name])
      material.color = Sketchup::Color.new(*mat_info[:color])
      material.alpha = mat_info[:alpha]
      puts "   ✓ 创建材质: #{mat_info[:name]}"
    else
      puts "   - 材质已存在: #{mat_info[:name]}"
    end
  end
  
  puts "   当前总材质数: #{materials.length}"
  
  # 2. 检查前端构建文件
  puts "\n2. 检查前端构建文件..."
  dist_path = 'D:/115/材质浏览器/frontend/dist/index.html'
  if File.exist?(dist_path)
    puts "   ✓ 前端构建文件存在: #{dist_path}"
    
    # 检查文件修改时间
    mtime = File.mtime(dist_path)
    puts "   构建时间: #{mtime}"
  else
    puts "   ❌ 前端构建文件不存在，请运行 npm run build"
    return
  end
  
  # 3. 重新加载插件
  puts "\n3. 重新加载插件..."
  load 'D:/115/材质浏览器/plugin/material_browser.rb'
  puts "   ✓ 插件重新加载成功"
  
  # 4. 测试材质数据获取
  puts "\n4. 测试材质数据获取..."
  
  # 创建临时对话框实例来测试数据获取
  test_dialog = MaterialBrowser::MaterialBrowserDialog.new
  materials_data = test_dialog.send(:get_materials_data)
  
  puts "   获取到材质数据:"
  materials_data.each_with_index do |mat, index|
    puts "   #{index + 1}. #{mat[:display_name]} (#{mat[:name]})"
    puts "      颜色: RGB(#{(mat[:color][0] * 255).to_i}, #{(mat[:color][1] * 255).to_i}, #{(mat[:color][2] * 255).to_i})"
    puts "      透明度: #{mat[:alpha]}"
    puts "      纹理: #{mat[:texture] || '无'}"
    puts "      图标数据: #{mat[:icon_data] ? mat[:icon_data][0..50] + '...' : '无'}"
  end
  
  # 5. 打开材质浏览器
  puts "\n5. 打开材质浏览器..."
  MaterialBrowser.show_material_browser
  puts "   ✓ 材质浏览器已打开"
  
  puts "\n" + "=" * 50
  puts "测试完成！"
  puts "=" * 50
  puts "\n使用说明:"
  puts "1. 材质浏览器窗口应该已经打开"
  puts "2. 点击'刷新'按钮可以重新加载材质"
  puts "3. 点击材质卡片可以应用材质到选中对象"
  puts "4. 如果有问题，请检查浏览器控制台 (F12)"
  
rescue => e
  puts "\n❌ 测试失败: #{e.message}"
  puts "错误详情:"
  puts e.backtrace.join("\n")
end
