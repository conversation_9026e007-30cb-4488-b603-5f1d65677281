# encoding: utf-8
# 测试材质功能的脚本

puts "开始测试材质功能..."

begin
  # 创建一些测试材质
  model = Sketchup.active_model
  materials = model.materials
  
  # 清除现有材质（可选）
  # materials.purge_unused
  
  # 创建测试材质
  puts "创建测试材质..."
  
  # 红色材质
  red_material = materials.add("测试红色")
  red_material.color = Sketchup::Color.new(255, 0, 0)
  red_material.alpha = 1.0
  
  # 蓝色半透明材质
  blue_material = materials.add("测试蓝色")
  blue_material.color = Sketchup::Color.new(0, 0, 255)
  blue_material.alpha = 0.7
  
  # 绿色材质
  green_material = materials.add("测试绿色")
  green_material.color = Sketchup::Color.new(0, 255, 0)
  green_material.alpha = 1.0
  
  puts "✅ 创建了 #{materials.length} 个材质"
  
  # 列出所有材质
  puts "\n当前材质列表："
  materials.each_with_index do |material, index|
    color = material.color
    puts "#{index + 1}. #{material.name} - RGB(#{color.red}, #{color.green}, #{color.blue}) - Alpha: #{material.alpha}"
  end
  
  # 重新加载插件
  puts "\n重新加载材质浏览器插件..."
  load 'D:/115/材质浏览器/plugin/material_browser.rb'
  
  puts "✅ 插件重新加载完成！"
  puts "\n现在可以打开材质浏览器测试："
  puts "MaterialBrowser.show_material_browser"
  
rescue => e
  puts "❌ 测试失败: #{e.message}"
  puts "错误详情: #{e.backtrace.first}"
end
