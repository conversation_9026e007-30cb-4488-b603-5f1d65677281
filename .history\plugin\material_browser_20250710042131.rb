# SketchUp 材质浏览器插件
# 作者: AI Assistant
# 版本: 1.0.0

require 'sketchup.rb'
require 'extensions.rb'

module MaterialBrowser
  
  # 插件信息
  PLUGIN_NAME = "材质浏览器"
  PLUGIN_VERSION = "1.0.0"
  
  # 创建扩展
  extension = SketchupExtension.new(PLUGIN_NAME, File.join(File.dirname(__FILE__), 'lib', 'material_browser_main.rb'))
  extension.description = "现代化的SketchUp材质浏览器，提供macOS风格的用户界面"
  extension.version = PLUGIN_VERSION
  extension.creator = "AI Assistant"
  extension.copyright = "2024"
  
  # 注册扩展
  Sketchup.register_extension(extension, true)
  
end
