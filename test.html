<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>材质浏览器测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f7;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-materials {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }
        .material-item {
            background: #f8f8f8;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        }
        .color-swatch {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            margin: 0 auto 8px;
            border: 1px solid #ddd;
        }
        button {
            background: #007aff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 4px;
        }
        button:hover {
            background: #0056cc;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>SketchUp 材质浏览器测试</h1>
        <p>这是一个测试页面，用于验证材质浏览器的功能。</p>
        
        <div>
            <button onclick="testGetMaterials()">获取材质列表</button>
            <button onclick="testCreateMaterial()">创建测试材质</button>
            <button onclick="testApplyMaterial()">应用材质</button>
        </div>
        
        <div id="materials-container" class="test-materials"></div>
    </div>

    <script>
        // 模拟材质数据
        const mockMaterials = [
            {
                name: "Red_Material",
                display_name: "红色材质",
                color: [1, 0, 0],
                alpha: 1,
                texture: null,
                use_alpha: false
            },
            {
                name: "Blue_Material", 
                display_name: "蓝色材质",
                color: [0, 0, 1],
                alpha: 0.8,
                texture: null,
                use_alpha: true
            },
            {
                name: "Wood_Material",
                display_name: "木材纹理",
                color: [0.6, 0.4, 0.2],
                alpha: 1,
                texture: "wood_texture.jpg",
                use_alpha: false
            }
        ];

        // 接收材质数据的函数
        window.receiveMaterials = function(materials) {
            console.log('收到材质数据:', materials);
            displayMaterials(materials);
        };

        // 模拟SketchUp回调
        window.sketchup = {
            callback: function(action, data) {
                console.log('调用SketchUp:', action, data);
                
                switch(action) {
                    case 'getMaterials':
                        // 模拟返回材质数据
                        setTimeout(() => {
                            window.receiveMaterials(mockMaterials);
                        }, 500);
                        break;
                    case 'applyMaterial':
                        alert('应用材质: ' + data);
                        break;
                    case 'createMaterial':
                        alert('创建材质: ' + JSON.stringify(data));
                        break;
                    case 'deleteMaterial':
                        alert('删除材质: ' + data);
                        break;
                    case 'importMaterial':
                        alert('导入材质');
                        break;
                    case 'exportMaterial':
                        alert('导出材质: ' + data);
                        break;
                }
            }
        };

        function testGetMaterials() {
            window.sketchup.callback('getMaterials');
        }

        function testCreateMaterial() {
            const materialData = {
                name: 'Test_Material_' + Date.now(),
                color: [Math.random(), Math.random(), Math.random()],
                alpha: 1,
                use_alpha: false
            };
            window.sketchup.callback('createMaterial', materialData);
        }

        function testApplyMaterial() {
            if (mockMaterials.length > 0) {
                window.sketchup.callback('applyMaterial', mockMaterials[0].name);
            }
        }

        function displayMaterials(materials) {
            const container = document.getElementById('materials-container');
            container.innerHTML = '';
            
            materials.forEach(material => {
                const item = document.createElement('div');
                item.className = 'material-item';
                
                const [r, g, b] = material.color;
                const colorHex = '#' + 
                    Math.round(r * 255).toString(16).padStart(2, '0') +
                    Math.round(g * 255).toString(16).padStart(2, '0') +
                    Math.round(b * 255).toString(16).padStart(2, '0');
                
                item.innerHTML = `
                    <div class="color-swatch" style="background-color: ${colorHex}; opacity: ${material.alpha}"></div>
                    <div>${material.display_name}</div>
                    <div style="font-size: 12px; color: #666;">${material.texture ? '纹理' : '纯色'}</div>
                    <button onclick="window.sketchup.callback('applyMaterial', '${material.name}')">应用</button>
                `;
                
                container.appendChild(item);
            });
        }

        // 页面加载完成后获取材质
        window.addEventListener('load', function() {
            testGetMaterials();
        });
    </script>
</body>
</html>
