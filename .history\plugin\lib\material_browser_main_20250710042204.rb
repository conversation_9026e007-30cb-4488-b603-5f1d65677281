# 材质浏览器主要功能模块

require 'sketchup.rb'
require 'json'

module MaterialBrowser
  
  class MaterialBrowserDialog
    
    def initialize
      @dialog = nil
      @materials_data = []
      setup_dialog
    end
    
    # 设置对话框
    def setup_dialog
      @dialog = UI::HtmlDialog.new(
        {
          :dialog_title => "材质浏览器",
          :preferences_key => "com.materialbrowser.plugin",
          :scrollable => false,
          :resizable => true,
          :width => 1000,
          :height => 700,
          :left => 100,
          :top => 100,
          :min_width => 800,
          :min_height => 600,
          :max_width => 1400,
          :max_height => 1000,
          :style => UI::HtmlDialog::STYLE_DIALOG
        }
      )
      
      # 设置HTML文件路径
      html_file = File.join(File.dirname(__FILE__), '..', '..', 'frontend', 'dist', 'index.html')
      if File.exist?(html_file)
        @dialog.set_file(html_file)
      else
        # 开发模式，使用本地服务器
        @dialog.set_url("http://localhost:5173")
      end
      
      setup_callbacks
    end
    
    # 设置JavaScript回调
    def setup_callbacks
      # 获取材质列表
      @dialog.add_action_callback("getMaterials") do |action_context|
        materials = get_materials_data
        @dialog.execute_script("window.receiveMaterials(#{materials.to_json})")
      end
      
      # 应用材质到选中的面
      @dialog.add_action_callback("applyMaterial") do |action_context, material_name|
        apply_material_to_selection(material_name)
      end
      
      # 创建新材质
      @dialog.add_action_callback("createMaterial") do |action_context, material_data|
        create_material(material_data)
      end
      
      # 删除材质
      @dialog.add_action_callback("deleteMaterial") do |action_context, material_name|
        delete_material(material_name)
      end
      
      # 导入材质
      @dialog.add_action_callback("importMaterial") do |action_context|
        import_material
      end
      
      # 导出材质
      @dialog.add_action_callback("exportMaterial") do |action_context, material_name|
        export_material(material_name)
      end
    end
    
    # 显示对话框
    def show
      @dialog.show
    end
    
    # 获取材质数据
    def get_materials_data
      materials = []
      Sketchup.active_model.materials.each do |material|
        material_data = {
          name: material.name,
          display_name: material.display_name,
          color: material.color.to_a[0..2], # RGB值
          alpha: material.alpha,
          texture: material.texture ? material.texture.filename : nil,
          use_alpha: material.use_alpha?
        }
        materials << material_data
      end
      materials
    end
    
    # 应用材质到选中对象
    def apply_material_to_selection(material_name)
      model = Sketchup.active_model
      material = model.materials[material_name]
      
      return unless material
      
      selection = model.selection
      if selection.empty?
        UI.messagebox("请先选择要应用材质的对象")
        return
      end
      
      model.start_operation("应用材质", true)
      
      selection.each do |entity|
        if entity.respond_to?(:material=)
          entity.material = material
        elsif entity.is_a?(Sketchup::Group) || entity.is_a?(Sketchup::ComponentInstance)
          entity.material = material
        end
      end
      
      model.commit_operation
      UI.messagebox("材质已应用到选中对象")
    end
    
    # 创建新材质
    def create_material(material_data)
      model = Sketchup.active_model
      materials = model.materials
      
      material = materials.add(material_data['name'])
      material.color = material_data['color'] if material_data['color']
      material.alpha = material_data['alpha'] if material_data['alpha']
      
      # 刷新材质列表
      refresh_materials
    end
    
    # 删除材质
    def delete_material(material_name)
      model = Sketchup.active_model
      material = model.materials[material_name]
      
      if material
        model.materials.remove(material)
        refresh_materials
        UI.messagebox("材质 '#{material_name}' 已删除")
      end
    end
    
    # 导入材质
    def import_material
      # 打开文件选择对话框
      file_path = UI.openpanel("选择材质文件", "", "材质文件|*.skm;*.jpg;*.png;*.bmp||")
      
      return unless file_path
      
      model = Sketchup.active_model
      materials = model.materials
      
      begin
        if File.extname(file_path).downcase == '.skm'
          # SketchUp材质文件
          material = materials.load(file_path)
        else
          # 图片文件
          material_name = File.basename(file_path, File.extname(file_path))
          material = materials.add(material_name)
          material.texture = file_path
        end
        
        refresh_materials
        UI.messagebox("材质导入成功")
      rescue => e
        UI.messagebox("导入失败: #{e.message}")
      end
    end
    
    # 导出材质
    def export_material(material_name)
      model = Sketchup.active_model
      material = model.materials[material_name]
      
      return unless material
      
      # 选择保存位置
      file_path = UI.savepanel("导出材质", "", "#{material_name}.skm")
      
      return unless file_path
      
      begin
        material.save_as(file_path)
        UI.messagebox("材质导出成功")
      rescue => e
        UI.messagebox("导出失败: #{e.message}")
      end
    end
    
    # 刷新材质列表
    def refresh_materials
      materials = get_materials_data
      @dialog.execute_script("window.receiveMaterials(#{materials.to_json})")
    end
    
  end
  
  # 全局实例
  @@dialog_instance = nil
  
  # 显示材质浏览器
  def self.show_material_browser
    @@dialog_instance ||= MaterialBrowserDialog.new
    @@dialog_instance.show
  end
  
  # 添加菜单项
  unless file_loaded?(__FILE__)
    menu = UI.menu("插件")
    menu.add_item("材质浏览器") { MaterialBrowser.show_material_browser }
    
    # 添加工具栏
    toolbar = UI::Toolbar.new("材质浏览器")
    cmd = UI::Command.new("材质浏览器") { MaterialBrowser.show_material_browser }
    cmd.tooltip = "打开材质浏览器"
    cmd.status_bar_text = "浏览和管理SketchUp材质"
    cmd.small_icon = File.join(File.dirname(__FILE__), "icons", "material_browser_16.png")
    cmd.large_icon = File.join(File.dirname(__FILE__), "icons", "material_browser_24.png")
    toolbar.add_item(cmd)
    toolbar.show
    
    file_loaded(__FILE__)
  end
  
end
